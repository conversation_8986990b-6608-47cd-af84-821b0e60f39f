import { Application } from '../../../../declarations';
import { Knex } from 'knex';
import * as Errors from '@feathersjs/errors';
import { currentUid } from '../../../../util/uid';
import logger from '../../../../logger';
import { getSysConstString } from '../../../../util/sys-const-string';
import { Student } from '../../school-admin/student/student.class';
import moment from 'moment';
import { dbRawRead } from '../../../../util/db-raw';

interface StudentInfo {
  uid: number;
  firstName: string;
  lastName: string;
  oen?: string;
  testWindows: { id: number; name: string }[];
}

interface SnapshotData {
  isSafeToUpdate: boolean;
  message: string;
  snapshotId: number;
  ssaisId: number;
  studentInfoJson: any;
}

interface MetaUpdate {
  id: number;
  value: any;
  key: string;
  key_namespace: string;
}

interface ValidationRequest {
  studentUid: string;
  testWindowId: string;
  updates?: MetaUpdate[]; // Optional - can validate without changes
  isDryRun?: boolean; // true = validation only, false = validation + application
  applyToUserMetas?: boolean; // true = apply to user_metas table
  applyToSnapshot?: boolean; // true = apply to snapshot JSON
}

interface ValidationResult {
  ErrorMessages: string[];
  warningMessages: string[];
  applied?: boolean; // Indicates if validation results were applied
  appliedToUserMetas?: boolean; // Indicates if applied to user_metas
  appliedToSnapshot?: boolean; // Indicates if applied to snapshot
  namespace?: string; // The namespace where results were applied
  currentStoredErrors?: {
    namespace: string;
    userMetasErrors: string[];
    snapshotErrors: string[];
    hasStoredErrors: boolean;
  };
}



export class StudentMetaUpdate {
  app: Application;
  knex: Knex;
  
  constructor(options: any, app: Application) {
    this.app = app;
    this.knex = app.get('knexClientWrite');
  }

  /**
   * Finds student information or assessment snapshot data.
   *
   * @param params - The parameters for the find operation.
   * @returns A promise that resolves to either student information or snapshot data.
   */
  async find(params: any): Promise<StudentInfo | SnapshotData> {
    const { oen, studentUid, testWindowId } = params.query;

    if (oen) {
      return this.getStudentInfoByOen(oen);
    }

    if (studentUid && !testWindowId) {
      return this.getStudentInfoByUid(parseInt(studentUid, 10));
    }

    if (studentUid && testWindowId) {
      return this.getSnapshotData(studentUid, testWindowId);
    }

    throw new Errors.BadRequest('Invalid query parameters for find method.');
  }

  /**
   * Updates a student's metadata.
   *
   * @param data - The data for the create operation.
   * @param params - The parameters for the create operation.
   * @returns A promise that resolves when the update is complete.
   */
  async create(data: any, params: any): Promise<any> {
    const { studentUid, snapshotIdToRevoke, ssaisId, studentInfoJson, updates, password } = data;

    if (!studentUid || !snapshotIdToRevoke || !ssaisId || !studentInfoJson || !updates || !Array.isArray(updates) || updates.length === 0) {
      throw new Errors.BadRequest('Missing required fields for update.');
    }

    const correctPassword = await getSysConstString(this.app, 'SUPPORT_META_UPDATE_PASSWORD');
    if (password !== correctPassword) {
      throw new Errors.NotAuthenticated('Incorrect password.');
    }

    const changedByUid = await currentUid(this.app, params);

    return this.knex.transaction(async (trx) => {
      // Process each meta update
      for (const metaToUpdate of updates) {
        const { id: metaId, value: newValue } = metaToUpdate;

        // Skip virtual entries (negative IDs) - they'll be handled in the user table update section
        if (metaId < 0) continue;

        const originalMeta = await trx('user_metas').where({ id: metaId }).first();

        if (!originalMeta) {
          throw new Errors.NotFound(`user_meta with id ${metaId} not found.`);
        }
        const from_value = originalMeta.value;

        // Step 1: Log the change
        logger.info('STUDENT_META_UPDATE_SUPPORT_TOOL_LOG', {
          changed_by_uid: changedByUid,
          user_meta_id: metaId,
          from_value: from_value,
          to_value: newValue,
          student_uid: studentUid,
          snapshot_id: snapshotIdToRevoke,
          ssais_id: ssaisId,
          student_info_json: studentInfoJson,
          updates: updates
        });

        // Step 2: Update the user_metas table
        await trx('user_metas')
          .where({ id: metaId })
          .update({ value: newValue, updated_by_uid: changedByUid, updated_on: trx.fn.now() });
      }

      // Step 3: Handle special cases like user's name by building a single update object
      const userTableUpdate: { [key: string]: any } = {};
      const updatedStudentInfoJson = JSON.parse(JSON.stringify(studentInfoJson));

      for (const metaToUpdate of updates) {
        const { id: metaId, value: newValue, key, key_namespace } = metaToUpdate;

        if (metaId < 0) {
          // Virtual entries: only update users table and users section in snapshot
          if (key_namespace === 'eqao_sdc') {
            if (key === 'FirstName') {
              userTableUpdate.first_name = newValue;
              updatedStudentInfoJson.users.first_name = newValue;
            } else if (key === 'LastName') {
              userTableUpdate.last_name = newValue;
              updatedStudentInfoJson.users.last_name = newValue;
            }
          }
        } else {
          // Real user_metas: update user_metas array in snapshot, and users table if it's FirstName/LastName
          const metaIndex = updatedStudentInfoJson.user_metas.findIndex((m: any) => m.id === metaId);
          if (metaIndex !== -1) {
            updatedStudentInfoJson.user_metas[metaIndex].value = newValue;
          } else {
            logger.warn('Meta ID not found in snapshot JSON during update', { metaId, snapshotIdToRevoke });
          }

          // Also update users table for FirstName/LastName real metas
          if (key_namespace === 'eqao_sdc') {
            if (key === 'FirstName') {
              userTableUpdate.first_name = newValue;
              updatedStudentInfoJson.users.first_name = newValue;
            } else if (key === 'LastName') {
              userTableUpdate.last_name = newValue;
              updatedStudentInfoJson.users.last_name = newValue;
            }
          }
        }
      }

      if (Object.keys(userTableUpdate).length > 0) {
        await trx('users').where({ id: studentUid }).update(userTableUpdate);
      }

      // Step 4: Revoke old snapshot and create new one
      await trx('school_student_asmt_info_snapshot').where('id', snapshotIdToRevoke).update({
        is_revoked: 1,
        revoked_by_uid: changedByUid,
        revoked_on: trx.fn.now(),
      });

      await trx('school_student_asmt_info_snapshot').insert({
        ssais_id: ssaisId,
        student_uid: studentUid,
        student_info_json: JSON.stringify(updatedStudentInfoJson),
        created_on: trx.fn.now(),
        created_by_uid: changedByUid,
        is_revoked: 0,
      });

      const updatedKeys = updates.map((u: MetaUpdate) => `${u.key_namespace}.${u.key}`).join(', ');
      return { success: true, message: `Successfully updated: ${updatedKeys}.` };
    });
  }

  /**
   * Validates student metadata changes and optionally applies validation results.
   *
   * @param data - The validation request data.
   * @param params - The parameters for the validation operation.
   * @returns A promise that resolves to validation results.
   */
  async patch(id: any, data: ValidationRequest, params: any): Promise<ValidationResult> {
    const {
      studentUid,
      testWindowId,
      updates = [],
      isDryRun = true,
      applyToUserMetas = false,
      applyToSnapshot = false
    } = data;

    if (!studentUid || !testWindowId) {
      throw new Errors.BadRequest('Missing required fields: studentUid and testWindowId are required for validation.');
    }

    // Get snapshot data to build validation context
    const snapshotData = await this.getSnapshotData(studentUid, testWindowId);

    // Apply pending changes to snapshot data to create validation data
    const validationData = await this.buildValidationData(
      snapshotData.studentInfoJson,
      updates,
      parseInt(testWindowId, 10)
    );

    // Call the validation service
    const validateResult = await this.app
      .service('private/schools/student/validate')
      .create(validationData);

    const errorMessages = (validateResult as any).ErrorMessages || [];
    const warningMessages = (validateResult as any).warningMessages || [];

    // Get current stored errors for display
    const currentStoredErrors = await this.getCurrentStoredErrors(studentUid, testWindowId);

    const validationResult: ValidationResult = {
      ErrorMessages: errorMessages,
      warningMessages: warningMessages,
      currentStoredErrors
    };

    // If not dry run, apply validation results to selected targets
    if (!isDryRun && (applyToUserMetas || applyToSnapshot)) {
      const changedByUid = await currentUid(this.app, params);
      const namespace = await this.determineNamespaceForValidation(studentUid, testWindowId);

      validationResult.applied = true;
      validationResult.namespace = namespace;

      // Apply to user_metas if requested
      if (applyToUserMetas) {
        await this.applyValidationToUserMetas(studentUid, testWindowId, errorMessages, changedByUid);
        validationResult.appliedToUserMetas = true;
      }

      // Apply to snapshot if requested
      if (applyToSnapshot) {
        await this.applyValidationToSnapshot(studentUid, testWindowId, errorMessages, changedByUid);
        validationResult.appliedToSnapshot = true;
      }
    }

    return validationResult;
  }

  /**
   * Gets current stored validation errors for a student and test window
   */
  private async getCurrentStoredErrors(studentUid: string, testWindowId: string): Promise<any> {
    const namespace = await this.determineNamespaceForValidation(studentUid, testWindowId);

    // Get stored validation errors from user_metas
    const storedErrors = await this.knex('user_metas')
      .where({
        uid: parseInt(studentUid),
        key: 'errMsg',
        key_namespace: namespace
      })
      .first();

    // Also get from snapshot if available
    const snapshotData = await this.getSnapshotData(studentUid, testWindowId);
    let snapshotErrors: string[] = [];

    if (snapshotData.studentInfoJson?.user_metas) {
      const snapshotErrMsg = snapshotData.studentInfoJson.user_metas.find((meta: any) =>
        meta.key === 'errMsg' && meta.key_namespace === namespace
      );
      if (snapshotErrMsg && snapshotErrMsg.value) {
        try {
          snapshotErrors = JSON.parse(snapshotErrMsg.value);
        } catch (e) {
          // If not JSON, treat as single error
          snapshotErrors = [snapshotErrMsg.value];
        }
      }
    }

    let userMetasErrors: string[] = [];
    if (storedErrors && storedErrors.value) {
      try {
        userMetasErrors = JSON.parse(storedErrors.value);
      } catch (e) {
        // If not JSON, treat as single error
        userMetasErrors = [storedErrors.value];
      }
    }

    return {
      namespace,
      userMetasErrors,
      snapshotErrors,
      hasStoredErrors: userMetasErrors.length > 0 || snapshotErrors.length > 0
    };
  }



  /**
   * Builds validation data by applying pending changes to snapshot data.
   * Reuses the convertData logic from school.class.ts
   */
  private async buildValidationData(studentInfoJson: any, updates: MetaUpdate[], testWindowId: number): Promise<any> {
    // Create a copy of the student info and apply pending changes
    const modifiedStudentInfo = JSON.parse(JSON.stringify(studentInfoJson));

    // Apply updates to the modified student info
    for (const update of updates) {
      if (update.id < 0) {
        // Virtual entries: update users table fields
        if (update.key === 'FirstName') {
          modifiedStudentInfo.users.first_name = update.value;
        } else if (update.key === 'LastName') {
          modifiedStudentInfo.users.last_name = update.value;
        }
      } else {
        // Real user_metas: find and update the value
        const metaIndex = modifiedStudentInfo.user_metas.findIndex((m: any) => m.id === update.id);
        if (metaIndex !== -1) {
          modifiedStudentInfo.user_metas[metaIndex].value = update.value;
        }
      }
    }

    // Get school context data needed for validation
    const schoolContext = await this.getSchoolContext(modifiedStudentInfo.users.id, testWindowId);

    // Determine which grade to validate based on the test window and student's classes
    const grade = await this.determineGradeForValidation(modifiedStudentInfo.users.id, testWindowId);

    // Convert meta for specific grade if needed
    let processedMetas = modifiedStudentInfo.user_metas;
    if (grade && grade !== 'G9') {
      processedMetas = this.convertGradeOnlyMeta(modifiedStudentInfo.user_metas, grade);
    }

    // Convert to validation format using the same logic as school.class.ts
    return this.convertDataForValidation(
      modifiedStudentInfo.users,
      processedMetas,
      schoolContext
    );
  }

  /**
   * Gets school context data needed for validation
   */
  private async getSchoolContext(studentUid: number, testWindowId: number): Promise<any> {
    // Get student's school information
    const studentSchoolQuery = `
      SELECT DISTINCT
        s.group_id as sch_group_id,
        s.is_sasn_login,
        s.is_private,
        s.lang,
        s.school_type,
        sc.id as school_class_id,
        sc.group_id as class_group_id,
        sc.name as class_name,
        sc.semester_id,
        ss.foreign_id as semester_foreign_id,
        tw.date_end as test_window_end_date
      FROM users u
      JOIN user_roles ur ON ur.uid = u.id AND ur.is_revoked = 0
      JOIN school_classes sc ON sc.group_id = ur.group_id
      JOIN schools s ON s.group_id = sc.schl_group_id
      JOIN school_semesters ss ON ss.id = sc.semester_id
      JOIN test_windows tw ON tw.id = ss.test_window_id
      WHERE u.id = ? AND tw.id = ?
      LIMIT 1
    `;

    const result = await dbRawRead(this.app, [studentUid, testWindowId], studentSchoolQuery);
    const schoolData = result[0];

    if (!schoolData) {
      throw new Errors.NotFound('School context not found for student and test window.');
    }

    return schoolData;
  }

  /**
   * Converts student data to validation format.
   * Based on convertData method from school.class.ts
   */
  private convertDataForValidation(student: any, student_meta: any[], schoolContext: any): any {
    const validateData: any = {};

    // Basic student and school information
    validateData['StudentID'] = String(student.id);
    validateData['SchGroupID'] = String(schoolContext.sch_group_id);
    validateData['IsPrivate'] = String(schoolContext.is_private);
    validateData['Language'] = schoolContext.lang;
    validateData['FirstName'] = student.first_name;
    validateData['LastName'] = student.last_name;
    validateData['IsSASNLogin'] = schoolContext.is_sasn_login;
    validateData['SchoolType'] = schoolContext.school_type;

    // Process user_metas, excluding FirstName and LastName which come from users table
    const notCheckingKeys = ['FirstName', 'LastName'];
    for (let i = 0; i < student_meta.length; i++) {
      if (notCheckingKeys.includes(student_meta[i].key)) {
        continue;
      }
      validateData[student_meta[i].key] = student_meta[i].value;
      // Convert '#' to empty string as per original logic
      if (validateData[student_meta[i].key] === '#') {
        validateData[student_meta[i].key] = '';
      }
    }

    // Determine namespace based on grade flags
    if (validateData['IS_G3'] === '1' && validateData['IS_G6'] === '' && validateData['IS_G9'] === '' && validateData['IS_G10'] === '') {
      validateData['Namespace'] = 'eqao_g3';
    }
    if (validateData['IS_G3'] === '' && validateData['IS_G6'] === '1' && validateData['IS_G9'] === '' && validateData['IS_G10'] === '') {
      validateData['Namespace'] = 'eqao_g6';
    }
    if (validateData['IS_G3'] === '' && validateData['IS_G6'] === '' && validateData['IS_G9'] === '1' && validateData['IS_G10'] === '') {
      validateData['Namespace'] = 'eqao_g9';
    }
    if (validateData['IS_G3'] === '' && validateData['IS_G6'] === '' && validateData['IS_G9'] === '' && validateData['IS_G10'] === '1') {
      validateData['Namespace'] = 'eqao_g10';
    }

    // Remove Grouping/ClassCode as they come from school_classes table
    delete validateData['Grouping'];
    delete validateData['ClassCode'];

    // Set class-specific data
    if (schoolContext.school_class_id && validateData['Namespace'] === 'eqao_g10') {
      validateData['Grouping'] = schoolContext.class_name;
      validateData['TestWindowEndDate'] = moment(schoolContext.test_window_end_date).format("YYYYMMDD");
    }

    if (schoolContext.school_class_id && (validateData['Namespace'] === 'eqao_g3' || validateData['Namespace'] === 'eqao_g6' || validateData['Namespace'] === 'eqao_g9')) {
      validateData['ClassCode'] = schoolContext.class_name;
      if (validateData['Namespace'] === 'eqao_g9') {
        validateData['ClassTermFormat'] = String(schoolContext.semester_foreign_id);
      }
      validateData['TestWindowEndDate'] = moment(schoolContext.test_window_end_date).format("YYYYMMDD");
    }

    return validateData;
  }

  /**
   * Applies validation results to user_metas table (before snapshot changes).
   * This mimics how the business validation system records errMsg in user_metas.
   */
  private async applyValidationToUserMetas(studentUid: string, testWindowId: string, validationErrors: string[], changedByUid: number): Promise<any> {
    // Get the appropriate namespace for this test window/grade
    const namespace = await this.determineNamespaceForValidation(studentUid, testWindowId);

    return this.knex.transaction(async (trx) => {
      // Check if errMsg already exists for this student and namespace
      const existingErrMsg = await trx('user_metas')
        .where({
          uid: studentUid,
          key: 'errMsg',
          key_namespace: namespace
        })
        .first();

      const errMsgValue = JSON.stringify(validationErrors);

      if (existingErrMsg) {
        // Update existing errMsg
        await trx('user_metas')
          .where({ id: existingErrMsg.id })
          .update({
            value: errMsgValue,
            updated_by_uid: changedByUid,
            updated_on: trx.fn.now()
          });
      } else {
        // Create new errMsg entry
        await trx('user_metas').insert({
          uid: studentUid,
          key: 'errMsg',
          key_namespace: namespace,
          value: errMsgValue,
          created_by_uid: changedByUid,
          updated_by_uid: changedByUid,
          created_on: trx.fn.now(),
          updated_on: trx.fn.now()
        });
      }

      return {
        success: true,
        message: `Applied ${validationErrors.length} validation errors to user_metas for namespace ${namespace}`,
        appliedTo: 'user_metas',
        namespace: namespace
      };
    });
  }

  /**
   * Applies validation results to snapshot JSON (for snapshot changes).
   * This updates the errMsg in the snapshot's user_metas array.
   */
  private async applyValidationToSnapshot(studentUid: string, testWindowId: string, validationErrors: string[], changedByUid: number): Promise<any> {
    // Get current snapshot data
    const snapshotData = await this.getSnapshotData(studentUid, testWindowId);
    const namespace = await this.determineNamespaceForValidation(studentUid, testWindowId);

    return this.knex.transaction(async (trx) => {
      // Modify the snapshot JSON to include errMsg
      const updatedStudentInfoJson = JSON.parse(JSON.stringify(snapshotData.studentInfoJson));

      if (!updatedStudentInfoJson.user_metas) {
        updatedStudentInfoJson.user_metas = [];
      }

      // Find existing errMsg in snapshot or create new one
      const existingErrMsgIndex = updatedStudentInfoJson.user_metas.findIndex(
        (meta: any) => meta.key === 'errMsg' && meta.key_namespace === namespace
      );

      const errMsgValue = JSON.stringify(validationErrors);

      if (existingErrMsgIndex >= 0) {
        // Update existing errMsg in snapshot
        updatedStudentInfoJson.user_metas[existingErrMsgIndex].value = errMsgValue;
      } else {
        // Add new errMsg to snapshot
        updatedStudentInfoJson.user_metas.push({
          key: 'errMsg',
          key_namespace: namespace,
          value: errMsgValue
        });
      }

      // Create new snapshot with updated errMsg
      await trx('school_student_asmt_info_snapshot').insert({
        ssais_id: snapshotData.ssaisId,
        student_uid: studentUid,
        student_info_json: JSON.stringify(updatedStudentInfoJson),
        created_on: trx.fn.now(),
        created_by_uid: changedByUid,
        is_revoked: 0,
      });

      // Revoke the old snapshot
      await trx('school_student_asmt_info_snapshot')
        .where('id', snapshotData.snapshotId)
        .update({
          is_revoked: 1,
          revoked_by_uid: changedByUid,
          revoked_on: trx.fn.now(),
        });

      return {
        success: true,
        message: `Applied ${validationErrors.length} validation errors to snapshot JSON for namespace ${namespace}`,
        appliedTo: 'snapshot',
        namespace: namespace
      };
    });
  }

  /**
   * Determines the appropriate namespace for validation based on student's grade and test window.
   */
  private async determineNamespaceForValidation(studentUid: string, testWindowId: string): Promise<string> {
    // Get the grade for this student and test window
    const grade = await this.determineGradeForValidation(parseInt(studentUid), parseInt(testWindowId));

    // Map grade to namespace with correct eqao_sdc_gX format
    if (grade === 'G3') return 'eqao_sdc_g3';
    if (grade === 'G6') return 'eqao_sdc_g6';
    if (grade === 'G9') return 'eqao_sdc_g9';
    if (grade === 'G10') return 'eqao_sdc_g10';

    // Default to eqao_sdc if grade cannot be determined
    return 'eqao_sdc';
  }

  /**
   * Converts student meta for specific grade validation.
   * Based on convertGradeOnlyMeta method from school.class.ts
   */
  private convertGradeOnlyMeta(student_meta: any[], grade: string): any[] {
    const returnMeta: any[] = [];
    const overlapVariables = Student.overlapVariables;

    student_meta.forEach((metaRecord: any) => {
      if (metaRecord.key === 'IS_G3' || metaRecord.key === 'IS_G6' || metaRecord.key === 'IS_G9' || metaRecord.key === 'IS_G10') {
        return;
      }
      if (grade === 'G3' && (metaRecord.key_namespace === 'eqao_sdc_g3' || overlapVariables.indexOf(metaRecord.key) !== -1)) {
        returnMeta.push(metaRecord);
      }
      if (grade === 'G6' && (metaRecord.key_namespace === 'eqao_sdc_g6' || overlapVariables.indexOf(metaRecord.key) !== -1)) {
        returnMeta.push(metaRecord);
      }
      if (grade === 'G9' && metaRecord.key_namespace === 'eqao_sdc') {
        returnMeta.push(metaRecord);
      }
      if (grade === 'G10' && (metaRecord.key_namespace === 'eqao_sdc_g10' || overlapVariables.indexOf(metaRecord.key) !== -1)) {
        returnMeta.push(metaRecord);
      }
    });

    // Add grade flags
    if (grade === 'G3') {
      returnMeta.push({ key: 'IS_G3', value: '1' });
      returnMeta.push({ key: 'IS_G6', value: '' });
      returnMeta.push({ key: 'IS_G9', value: '' });
      returnMeta.push({ key: 'IS_G10', value: '' });
    }
    if (grade === 'G6') {
      returnMeta.push({ key: 'IS_G3', value: '' });
      returnMeta.push({ key: 'IS_G6', value: '1' });
      returnMeta.push({ key: 'IS_G9', value: '' });
      returnMeta.push({ key: 'IS_G10', value: '' });
    }
    if (grade === 'G9') {
      returnMeta.push({ key: 'IS_G3', value: '' });
      returnMeta.push({ key: 'IS_G6', value: '' });
      returnMeta.push({ key: 'IS_G9', value: '1' });
      returnMeta.push({ key: 'IS_G10', value: '' });
    }
    if (grade === 'G10') {
      returnMeta.push({ key: 'IS_G3', value: '' });
      returnMeta.push({ key: 'IS_G6', value: '' });
      returnMeta.push({ key: 'IS_G9', value: '' });
      returnMeta.push({ key: 'IS_G10', value: '1' });
    }

    return returnMeta;
  }

  /**
   * Determines which grade to validate based on student's class enrollment
   */
  private async determineGradeForValidation(studentUid: number, testWindowId: number): Promise<string | null> {
    const gradeQuery = `
      SELECT DISTINCT sc.group_type
      FROM user_roles ur
      JOIN school_classes sc ON sc.group_id = ur.group_id
      JOIN school_semesters ss ON ss.id = sc.semester_id
      JOIN test_windows tw ON tw.id = ss.test_window_id
      WHERE ur.uid = ? AND tw.id = ?
      ORDER BY sc.group_type
    `;

    const classes = await dbRawRead(this.app, [studentUid, testWindowId], gradeQuery);

    // Return the first grade found
    for (const classInfo of classes) {
      if (classInfo.group_type === 'EQAO_G3') return 'G3';
      if (classInfo.group_type === 'EQAO_G6') return 'G6';
      if (classInfo.group_type === 'EQAO_G9') return 'G9';
      if (classInfo.group_type === 'EQAO_G10') return 'G10';
    }

    throw new Errors.NotFound('Cannot determine student grade for validation.');
  }

  /**
   * Retrieves student information by OEN.
   *
   * @param oen - The student's OEN.
   * @returns A promise that resolves to the student's information.
   * @private
   */
  private async getStudentInfoByOen(oen: string): Promise<StudentInfo> {
    const studentMeta = await this.knex('user_metas')
      .where({ key_namespace: 'eqao_sdc', key: 'StudentOEN', value: oen })
      .first('uid');

    if (!studentMeta) {
      throw new Errors.NotFound('Student not found for the given OEN.');
    }

    const studentInfo = await this.getStudentInfoByUid(studentMeta.uid);
    return { ...studentInfo, oen };
  }

  /**
   * Retrieves student information by UID.
   *
   * @param studentUid - The student's UID.
   * @returns A promise that resolves to the student's information.
   * @private
   */
  private async getStudentInfoByUid(studentUid: number): Promise<StudentInfo> {
    const student = await this.knex('users')
      .where('id', studentUid)
      .first('id as uid', 'first_name as firstName', 'last_name as lastName');

    if (!student) {
      throw new Errors.NotFound('Student not found for the given UID.');
    }

    // Fetch the student's OEN
    const oenMeta = await this.knex('user_metas')
      .where({ uid: studentUid, key_namespace: 'eqao_sdc', key: 'StudentOEN' })
      .first('value');

    const testWindows = await this.getStudentTestWindows(studentUid);

    return { ...student, oen: oenMeta?.value, testWindows };
  }

  /**
   * Retrieves test windows for a student.
   *
   * @param studentUid - The student's UID.
   * @returns A promise that resolves to the student's test windows.
   * @private
   */
  private async getStudentTestWindows(studentUid: number): Promise<{ id: number; name: string }[]> {
    return await this.knex('student_reports as sr')
      .join('test_attempts as ta', 'ta.id', 'sr.attempt_id')
      .join('test_window_td_alloc_rules as twtdar', 'twtdar.id', 'ta.twtdar_id')
      .join('test_windows as tw', 'tw.id', 'twtdar.test_window_id')
      .where('sr.uid', studentUid)
      .where('sr.is_revoked', 0)
      .where('sr.is_isr', 1)
      .distinct('tw.id', 'tw.title as name')
      .orderBy('tw.id', 'desc');
  }

  /**
   * Retrieves snapshot data for a student and test window.
   *
   * @param studentUid - The student's UID.
   * @param testWindowId - The test window's ID.
   * @returns A promise that resolves to the snapshot data.
   * @private
   */
  private async getSnapshotData(studentUid: string, testWindowId: string): Promise<SnapshotData> {
    // Step 1: Check how many ISRs
    const isrCountResult = await this.knex('student_reports as sr')
      .join('test_attempts as ta', 'ta.id', 'sr.attempt_id')
      .join('test_window_td_alloc_rules as twtdar', 'twtdar.id', 'ta.twtdar_id')
      .where('sr.uid', studentUid)
      .where('sr.is_revoked', 0)
      .where('sr.is_isr', 1)
      .where('twtdar.test_window_id', testWindowId)
      .count('sr.id as count');

    const isrCount = isrCountResult[0].count as number;
    const isSafeToUpdate = isrCount === 1;
    const message = isSafeToUpdate
      ? `Safe to update: Student has ${isrCount} ISR for this test window.`
      : `Warning: Student has ${isrCount} ISRs for this test window.`;

    // Step 3: Find student snapshot
    const snapshot = await this.knex('school_student_asmt_info_snapshot as snapshot')
      .join('school_student_asmt_info_signoffs as ssais', {
        'ssais.id': 'snapshot.ssais_id',
      })
      .where('snapshot.is_revoked', 0)
      .where('ssais.is_revoked', 0)
      .where('ssais.test_window_id', testWindowId)
      .where('snapshot.student_uid', studentUid)
      .first('snapshot.id as snapshotId', 'snapshot.student_info_json as studentInfoJson', 'ssais.id as ssaisId');

    if (!snapshot) {
      throw new Errors.NotFound('Snapshot not found for the given student and test window.');
    }

    return {
      isSafeToUpdate,
      message,
      snapshotId: snapshot.snapshotId,
      ssaisId: snapshot.ssaisId,
      studentInfoJson: JSON.parse(snapshot.studentInfoJson),
    };
  }
} 