import { Component, EventEmitter, Input, OnInit, Output, ViewChild, ElementRef } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { LangService } from 'src/app/core/lang.service';
import { FormControl } from '@angular/forms';
import { map, startWith } from 'rxjs/operators';
import {COMMA, ENTER} from '@angular/cdk/keycodes';
import {MatChipInputEvent} from '@angular/material/chips';
import { Observable } from 'rxjs';
import {MatAutocompleteSelectedEvent} from '@angular/material/autocomplete';

export enum AssignmentState {
  IDLE = 'IDLE',
  SENDING = 'SENDING',
  SENT = 'SENT',
  ERROR = 'ERROR'
}

@Component({
  selector: 'panel-sl-new-assignment',
  templateUrl: './panel-sl-new-assignment.component.html',
  styleUrls: ['./panel-sl-new-assignment.component.scss']
})
export class PanelSlNewAssignmentComponent implements OnInit {
  @Input() prefillAssignees: any[];
  @Input() items: any[];
  @Input() markingWindowId: number;
  @Input() markingWindowGroupId: number;
  @Input() isLocked : boolean;
  @Output() close = new EventEmitter<boolean>();

  @ViewChild('assigneeInput') assigneeInput: ElementRef<HTMLInputElement>;

  isLoaded:boolean = false;
  selectable:boolean = true;
  removable:boolean = true;
  allScorers: any[] = [];
  assignees: any[] = [];
  filteredAssignees: Observable<string[]>;
  assigneeCtrl = new FormControl();
  separatorKeysCodes: number[] = [ENTER, COMMA];
  assignmentItemId: number;
  isSkipTraining: boolean = false;

  state:AssignmentState = AssignmentState.IDLE;

  constructor(private auth:AuthService, public lang:LangService) {
    this.filteredAssignees = this.assigneeCtrl.valueChanges.pipe(
      startWith(null),
      map((value: string | null) => value ? this._filter(value) : this.allScorers.slice())
    );
  }

  ngOnInit(): void {
    this.loadAssignees();
  }

  loadAssignees(){
    this.auth.apiGet('public/scor-lead/accounts', this.markingWindowId, {query: {mrkg_group_id: this.markingWindowGroupId}}).then(accts => {
      this.isLoaded = true;
      this.allScorers = accts.filter(a => a.account_type == 'scor-scor');
      for(let pr of this.prefillAssignees) {
        if(!this.assignees.find(r => r.uid == pr.uid)) {
          this.assignees.push(pr);
        }
      }
    });
  }

  cancel() {
    this.close.emit(true);
  }

  removeAssignee(assignee: any): void {
    const index = this.assignees.findIndex((element) => element.uid == assignee.uid);
    if (index >= 0) {
      this.assignees.splice(index, 1);
    }
  }

  private _filter(value: any): string[] {
    if(value.uid) {
      return this.allScorers.filter(rec => rec.uid == value.uid);
    }
    else {
      let filterValue = value.toLowerCase();
      let filteredAssignees = [];
      for(let rec of this.allScorers) {
        if((rec.first_name + " " + rec.last_name)?.toLowerCase().includes(filterValue)) {
          filteredAssignees.push(rec);
        }
        else if(rec.contact_email?.toLowerCase().includes(filterValue)) {
          filteredAssignees.push(rec);
        }
      }
      return filteredAssignees;
    }
  }

  addAssignee(event: MatChipInputEvent): void {
    console.log("ADD", event);
    const value = (event.value || '').trim();
    event.input.value = '';
    this.assigneeCtrl.setValue(null);
  }

  selectedAssignee(event: MatAutocompleteSelectedEvent): void {
    console.log(event.option.value)
    if(!this.assignees.find(r => r.uid == event.option.value.uid)) {
      this.assignees.push(event.option.value);
    }
    this.assigneeInput.nativeElement.value = '';
    this.assigneeCtrl.setValue(null);
  }

  fitToAssign(){
    return this.state == AssignmentState.IDLE && this.assignmentItemId && this.assignees.length > 0
  }

  async assign() {
    this.state = AssignmentState.SENDING;
    let itemIds;
    if (this.assignmentItemId == -1){
      itemIds = this.items.map(item => item.window_item_id)
    } else {
      itemIds = [this.assignmentItemId]
    }
    await this.auth.apiCreate('/public/scor-lead/item-scorer', {
      assigneeUids:this.assignees.map(r => r.uid),
      itemIds: itemIds,
      isSkipTraining: this.isSkipTraining
    })
    this.state = AssignmentState.SENT;
    setTimeout(() => {
      this.close.emit(true);
    }, 1000);
  }
}
