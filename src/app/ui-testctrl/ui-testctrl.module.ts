import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import { UiPartialModule } from '../ui-partial/ui-partial.module';
import { FullCalendarModule } from '@fullcalendar/angular';
import { MarkdownModule, MarkedOptions } from 'ngx-markdown';
import { ViewTcCreateAccountComponent } from './view-tc-create-account/view-tc-create-account.component';
import { ViewTcDashboardComponent } from './view-tc-dashboard/view-tc-dashboard.component';
import { ViewTcNewTestWindowComponent } from './view-tc-new-test-window/view-tc-new-test-window.component';
import { ViewTcTestWindowComponent } from './view-tc-test-window/view-tc-test-window.component';
import { ViewTcAccountsComponent } from './view-tc-accounts/view-tc-accounts.component';
import { ViewTcItemBankComponent } from './view-tc-item-bank/view-tc-item-bank.component';
import { ViewTcItemBankItemComponent } from './view-tc-item-bank-item/view-tc-item-bank-item.component';
import { ViewTcItemBankFrameworkComponent } from './view-tc-item-bank-framework/view-tc-item-bank-framework.component';
import { ViewTcTestDesignComponent } from './view-tc-test-design/view-tc-test-design.component';
import { ViewTcInstitutionsComponent } from './view-tc-institutions/view-tc-institutions.component';
import { UiTestadminModule } from '../ui-testadmin/ui-testadmin.module';
import { ViewTcSebGenComponent } from './view-tc-seb-gen/view-tc-seb-gen.component';
import { UiTestrunnerModule } from '../ui-testrunner/ui-testrunner.module';
import { ViewTcReportingComponent } from './view-tc-reporting/view-tc-reporting.component';
import { UiTestctrlRoutingModule } from './ui-testctrl-routing.module';
import { ViewTcBoardsSchoolsComponent } from './view-tc-boards-schools/view-tc-boards-schools.component';
import { ViewTcStudentsTeachersClassesComponent } from './view-tc-students-teachers-classes/view-tc-students-teachers-classes.component';
import { TcTableBoardsComponent } from './tc-table-boards/tc-table-boards.component';
import { TcTableSchoolsComponent } from './tc-table-schools/tc-table-schools.component';
import { TcTableStudentsComponent } from './tc-table-students/tc-table-students.component';
import { TcTableTeachersComponent } from './tc-table-teachers/tc-table-teachers.component';
import { TcTableClassesComponent } from './tc-table-classes/tc-table-classes.component';
import { TcTableCommonComponent } from './tc-table-common/tc-table-common.component';
import { TcTableCommonImportComponent } from './tc-table-common-import/tc-table-common-import.component';
import { ViewTcSchoolComponent } from './view-tc-school/view-tc-school.component';
import { ViewTcBoardsComponent } from './view-tc-boards/view-tc-boards.component';
import { ViewTcClassesComponent } from './view-tc-classes/view-tc-classes.component';
import { ViewTcAsmtSessionItemAnalysisComponent } from './view-tc-asmt-session-item-analysis/view-tc-asmt-session-item-analysis.component';
import { ViewTcTeachersComponent } from './view-tc-teachers/view-tc-teachers.component';
import { ViewTcStudentsComponent } from './view-tc-students/view-tc-students.component';
import { PanelTwtarComponent } from './panel-twtar/panel-twtar.component';
import { AgGridModule } from 'ag-grid-angular';
import { PanelReportedIssuesComponent } from './panel-reported-issues/panel-reported-issues.component';
import { PanelStudentLookupComponent } from './panel-student-lookup/panel-student-lookup.component';
import { PanelTcStudentAttemptInfoComponent } from './panel-tc-student-attempt-info/panel-tc-student-attempt-info.component';
import { ViewTcirDashboardTempComponent } from './view-tcir-dashboard-temp/view-tcir-dashboard-temp.component';
import { PanelStudentExceptionsComponent } from './panel-student-exceptions/panel-student-exceptions.component';
import { ViewTcdeDashboardComponent } from './view-tcde-dashboard/view-tcde-dashboard.component';
import { PanelRealTimeAuditsComponent } from './panel-real-time-audits/panel-real-time-audits.component';
import { PanelUnsubmissionRequestsComponent } from './panel-unsubmission-requests/panel-unsubmission-requests.component';
import { PanelValidateReportsComponent } from './panel-validate-reports/panel-validate-reports.component';
import { ViewTcNotificationsComponent } from './view-tc-notifications/view-tc-notifications.component';
import { PanelMessageComposerComponent } from './view-tc-notifications/panel-message-composer/panel-message-composer.component';
import { TcMessageComponent } from './view-tc-notifications/tc-message/tc-message.component';
import { TcModalUploadScanComponent } from './tc-modal-upload-scan/tc-modal-upload-scan.component';
import { WidgetExpectedAnswersComponent } from '../ui-item-maker/widget-expected-answers/widget-expected-answers.component';
import { UiItemMakerModule } from '../ui-item-maker/ui-item-maker.module';
import { DiffViewCell } from './view-tcde-dashboard/components/diff-view-cell/diff-view-cell.component';
import { PanelTestWindowTableComponent } from './panel-test-window-table/panel-test-window-table.component';
import { TcModalCopyTwtdarSettingComponent } from './tc-modal-copy-twtdar-setting/tc-modal-copy-twtdar-setting.component';
import { PanelUploadScansComponent } from './panel-upload-scans/panel-upload-scans.component';
import { PanelAppealsComponent } from './panel-appeals/panel-appeals.component';
import { PanelSessionsInitComponent } from './panel-sessions-init/panel-sessions-init.component';
import { ViewTcRolesComponent } from './view-tc-roles/view-tc-roles.component';
import { panelWithholdExemptModalComponent } from './panel-withhold-exempt-modal/panel-withhold-exempt-modal.component';
import { PanelIndividualOverridesComponent } from './panel-individual-overrides/panel-individual-overrides.component';
import { TcdeModalPsychConfigModalComponent } from './tcde-modal-psych-config/tcde-modal-psych-config.component';
import { TcModalIsrGenerationComponent } from './view-tcde-dashboard/components/tc-modal-isr-generation/tc-modal-isr-generation.component';
import { ScoringWindowSetupComponent } from './view-tc-test-window/scoring-window-setup/scoring-window-setup.component';

@NgModule({
    imports: [
      CommonModule,
      FullCalendarModule,
      ReactiveFormsModule,
      UiPartialModule,
      UiTestadminModule,
      MarkdownModule.forRoot({
        markedOptions: {
          provide: MarkedOptions,
          useValue: {
            gfm: true,
            tables: true,
            breaks: true,
            pedantic: false,
            sanitize: false,
            smartLists: true,
            smartypants: false,
          },
        },
      }),
      UiTestrunnerModule,
      FormsModule,
      UiTestctrlRoutingModule,
      AgGridModule.withComponents([DiffViewCell]),
      UiItemMakerModule
    ],
  declarations: [
    ViewTcCreateAccountComponent,
    ViewTcDashboardComponent,
    ViewTcNewTestWindowComponent,
    ViewTcTestWindowComponent,
    ViewTcAccountsComponent,
    ViewTcItemBankComponent,
    ViewTcItemBankItemComponent,
    ViewTcItemBankFrameworkComponent,
    ViewTcTestDesignComponent,
    ViewTcInstitutionsComponent,
    ViewTcSebGenComponent,
    ViewTcReportingComponent,
    ViewTcBoardsSchoolsComponent,
    ViewTcStudentsTeachersClassesComponent,
    TcTableBoardsComponent,
    TcTableSchoolsComponent,
    TcTableStudentsComponent,
    TcTableTeachersComponent,
    TcTableClassesComponent,
    TcTableCommonComponent,
    TcTableCommonImportComponent,
    ViewTcSchoolComponent,
    ViewTcBoardsComponent,
    ViewTcClassesComponent,
    ViewTcAsmtSessionItemAnalysisComponent,
    ViewTcTeachersComponent,
    ViewTcStudentsComponent,
    PanelTwtarComponent,
    PanelReportedIssuesComponent,
    PanelStudentLookupComponent,
    PanelTcStudentAttemptInfoComponent,
    ViewTcirDashboardTempComponent,
    PanelStudentExceptionsComponent,
    ViewTcdeDashboardComponent,
    PanelRealTimeAuditsComponent,
    PanelUnsubmissionRequestsComponent,
    PanelValidateReportsComponent,
    ViewTcNotificationsComponent,
    PanelMessageComposerComponent,
    TcMessageComponent,
    PanelUploadScansComponent,
    TcModalUploadScanComponent,
    DiffViewCell,
    PanelTestWindowTableComponent,
    TcModalCopyTwtdarSettingComponent,
    PanelUploadScansComponent,
    PanelAppealsComponent,
    PanelSessionsInitComponent,
    ViewTcRolesComponent,
    panelWithholdExemptModalComponent,
    PanelIndividualOverridesComponent,
    TcdeModalPsychConfigModalComponent,
    TcModalIsrGenerationComponent,
    ScoringWindowSetupComponent,
    ViewTcRolesComponent,
  ],
  exports: [
    TcTableCommonComponent,
    TcTableCommonImportComponent
  ]
})
export class UiTestctrlModule { }
