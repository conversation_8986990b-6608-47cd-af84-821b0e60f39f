import { Component, Input, OnInit } from '@angular/core';
import { ElementType, IContentElement, IQuestionConfig } from '../models';
import { QuestionPubSub } from '../question-runner/pubsub/question-pubsub';
import { EditSelectionService } from '../edit-selection.service';

export const TEST_RUNNER_WIDTH_EM = 38;

@Component({
  selector: 'element-render',
  templateUrl: './element-render.component.html',
  styleUrls: ['./element-render.component.scss']
})
export class ElementRenderComponent implements OnInit {

  @Input() contentElement: IContentElement;
  @Input() isLocked: boolean;
  @Input() questionState: any;
  @Input() isShowSolution?: boolean;
  @Input() allowSubtitles?: boolean;
  @Input() allowTranscripts?: boolean;
  @Input() allowAudioPlaybackSpeed?: boolean;
  @Input() allowVideoPlaybackSpeed?: boolean;
  @Input() questionPubSub?: QuestionPubSub;
  @Input() frameWorkTags:{slug:string}[];
  @Input() opinionEssayEditable:boolean;
  @Input() textInputChange:(args:any) => void;
  @Input() fromIssueReviewer: boolean;
  @Input() cancelOverrideResponse: boolean;
  @Input() currentQuestionMeta: {[key:string]: any};
  
  ElementType = ElementType;

  constructor(
    public editSelection: EditSelectionService
  ) { }

  ngOnInit() {
  }
  
  isSelectedEdit() {
    return this.questionPubSub?.selectedEntry?.id && this.questionPubSub?.selectedEntry?.id === this.contentElement.entryId;
  }

  getBorder() {
    if(this.isSelectedEdit()) {
      return this.questionPubSub?.selectedEntry?.border || 0;
    } else {
      return 0;
    }
  }
}
