
// List of character combinations which turn into special charcters when put into HTML
// Used for authoring comment highlighter tool (highlighter.service)
// Source: https://html.spec.whatwg.org/multipage/named-characters.html, https://html.spec.whatwg.org/entities.json 
export const namedCharRefs = new Set([
  "&AElig",
  "&AElig;",
  "&AMP",
  "&AMP;",
  "&Aacute",
  "&Aacute;",
  "&Abreve;",
  "&Acirc",
  "&Acirc;",
  "&Acy;",
  "&Afr;",
  "&Agrave",
  "&Agrave;",
  "&Alpha;",
  "&Amacr;",
  "&And;",
  "&Aogon;",
  "&Aopf;",
  "&ApplyFunction;",
  "&Aring",
  "&Aring;",
  "&Ascr;",
  "&Assign;",
  "&Atilde",
  "&Atilde;",
  "&Auml",
  "&Auml;",
  "&Backslash;",
  "&Barv;",
  "&Barwed;",
  "&<PERSON><PERSON>;",
  "&<PERSON>;",
  "&<PERSON><PERSON>;",
  "&Beta;",
  "&Bfr;",
  "&Bopf;",
  "&Breve;",
  "&Bscr;",
  "&Bumpeq;",
  "&CHcy;",
  "&COPY",
  "&COPY;",
  "&Cacute;",
  "&Cap;",
  "&CapitalDifferentialD;",
  "&Cayleys;",
  "&Ccaron;",
  "&Ccedil",
  "&Ccedil;",
  "&Ccirc;",
  "&Cconint;",
  "&Cdot;",
  "&Cedilla;",
  "&CenterDot;",
  "&Cfr;",
  "&Chi;",
  "&CircleDot;",
  "&CircleMinus;",
  "&CirclePlus;",
  "&CircleTimes;",
  "&ClockwiseContourIntegral;",
  "&CloseCurlyDoubleQuote;",
  "&CloseCurlyQuote;",
  "&Colon;",
  "&Colone;",
  "&Congruent;",
  "&Conint;",
  "&ContourIntegral;",
  "&Copf;",
  "&Coproduct;",
  "&CounterClockwiseContourIntegral;",
  "&Cross;",
  "&Cscr;",
  "&Cup;",
  "&CupCap;",
  "&DD;",
  "&DDotrahd;",
  "&DJcy;",
  "&DScy;",
  "&DZcy;",
  "&Dagger;",
  "&Darr;",
  "&Dashv;",
  "&Dcaron;",
  "&Dcy;",
  "&Del;",
  "&Delta;",
  "&Dfr;",
  "&DiacriticalAcute;",
  "&DiacriticalDot;",
  "&DiacriticalDoubleAcute;",
  "&DiacriticalGrave;",
  "&DiacriticalTilde;",
  "&Diamond;",
  "&DifferentialD;",
  "&Dopf;",
  "&Dot;",
  "&DotDot;",
  "&DotEqual;",
  "&DoubleContourIntegral;",
  "&DoubleDot;",
  "&DoubleDownArrow;",
  "&DoubleLeftArrow;",
  "&DoubleLeftRightArrow;",
  "&DoubleLeftTee;",
  "&DoubleLongLeftArrow;",
  "&DoubleLongLeftRightArrow;",
  "&DoubleLongRightArrow;",
  "&DoubleRightArrow;",
  "&DoubleRightTee;",
  "&DoubleUpArrow;",
  "&DoubleUpDownArrow;",
  "&DoubleVerticalBar;",
  "&DownArrow;",
  "&DownArrowBar;",
  "&DownArrowUpArrow;",
  "&DownBreve;",
  "&DownLeftRightVector;",
  "&DownLeftTeeVector;",
  "&DownLeftVector;",
  "&DownLeftVectorBar;",
  "&DownRightTeeVector;",
  "&DownRightVector;",
  "&DownRightVectorBar;",
  "&DownTee;",
  "&DownTeeArrow;",
  "&Downarrow;",
  "&Dscr;",
  "&Dstrok;",
  "&ENG;",
  "&ETH",
  "&ETH;",
  "&Eacute",
  "&Eacute;",
  "&Ecaron;",
  "&Ecirc",
  "&Ecirc;",
  "&Ecy;",
  "&Edot;",
  "&Efr;",
  "&Egrave",
  "&Egrave;",
  "&Element;",
  "&Emacr;",
  "&EmptySmallSquare;",
  "&EmptyVerySmallSquare;",
  "&Eogon;",
  "&Eopf;",
  "&Epsilon;",
  "&Equal;",
  "&EqualTilde;",
  "&Equilibrium;",
  "&Escr;",
  "&Esim;",
  "&Eta;",
  "&Euml",
  "&Euml;",
  "&Exists;",
  "&ExponentialE;",
  "&Fcy;",
  "&Ffr;",
  "&FilledSmallSquare;",
  "&FilledVerySmallSquare;",
  "&Fopf;",
  "&ForAll;",
  "&Fouriertrf;",
  "&Fscr;",
  "&GJcy;",
  "&GT",
  "&GT;",
  "&Gamma;",
  "&Gammad;",
  "&Gbreve;",
  "&Gcedil;",
  "&Gcirc;",
  "&Gcy;",
  "&Gdot;",
  "&Gfr;",
  "&Gg;",
  "&Gopf;",
  "&GreaterEqual;",
  "&GreaterEqualLess;",
  "&GreaterFullEqual;",
  "&GreaterGreater;",
  "&GreaterLess;",
  "&GreaterSlantEqual;",
  "&GreaterTilde;",
  "&Gscr;",
  "&Gt;",
  "&HARDcy;",
  "&Hacek;",
  "&Hat;",
  "&Hcirc;",
  "&Hfr;",
  "&HilbertSpace;",
  "&Hopf;",
  "&HorizontalLine;",
  "&Hscr;",
  "&Hstrok;",
  "&HumpDownHump;",
  "&HumpEqual;",
  "&IEcy;",
  "&IJlig;",
  "&IOcy;",
  "&Iacute",
  "&Iacute;",
  "&Icirc",
  "&Icirc;",
  "&Icy;",
  "&Idot;",
  "&Ifr;",
  "&Igrave",
  "&Igrave;",
  "&Im;",
  "&Imacr;",
  "&ImaginaryI;",
  "&Implies;",
  "&Int;",
  "&Integral;",
  "&Intersection;",
  "&InvisibleComma;",
  "&InvisibleTimes;",
  "&Iogon;",
  "&Iopf;",
  "&Iota;",
  "&Iscr;",
  "&Itilde;",
  "&Iukcy;",
  "&Iuml",
  "&Iuml;",
  "&Jcirc;",
  "&Jcy;",
  "&Jfr;",
  "&Jopf;",
  "&Jscr;",
  "&Jsercy;",
  "&Jukcy;",
  "&KHcy;",
  "&KJcy;",
  "&Kappa;",
  "&Kcedil;",
  "&Kcy;",
  "&Kfr;",
  "&Kopf;",
  "&Kscr;",
  "&LJcy;",
  "&LT",
  "&LT;",
  "&Lacute;",
  "&Lambda;",
  "&Lang;",
  "&Laplacetrf;",
  "&Larr;",
  "&Lcaron;",
  "&Lcedil;",
  "&Lcy;",
  "&LeftAngleBracket;",
  "&LeftArrow;",
  "&LeftArrowBar;",
  "&LeftArrowRightArrow;",
  "&LeftCeiling;",
  "&LeftDoubleBracket;",
  "&LeftDownTeeVector;",
  "&LeftDownVector;",
  "&LeftDownVectorBar;",
  "&LeftFloor;",
  "&LeftRightArrow;",
  "&LeftRightVector;",
  "&LeftTee;",
  "&LeftTeeArrow;",
  "&LeftTeeVector;",
  "&LeftTriangle;",
  "&LeftTriangleBar;",
  "&LeftTriangleEqual;",
  "&LeftUpDownVector;",
  "&LeftUpTeeVector;",
  "&LeftUpVector;",
  "&LeftUpVectorBar;",
  "&LeftVector;",
  "&LeftVectorBar;",
  "&Leftarrow;",
  "&Leftrightarrow;",
  "&LessEqualGreater;",
  "&LessFullEqual;",
  "&LessGreater;",
  "&LessLess;",
  "&LessSlantEqual;",
  "&LessTilde;",
  "&Lfr;",
  "&Ll;",
  "&Lleftarrow;",
  "&Lmidot;",
  "&LongLeftArrow;",
  "&LongLeftRightArrow;",
  "&LongRightArrow;",
  "&Longleftarrow;",
  "&Longleftrightarrow;",
  "&Longrightarrow;",
  "&Lopf;",
  "&LowerLeftArrow;",
  "&LowerRightArrow;",
  "&Lscr;",
  "&Lsh;",
  "&Lstrok;",
  "&Lt;",
  "&Map;",
  "&Mcy;",
  "&MediumSpace;",
  "&Mellintrf;",
  "&Mfr;",
  "&MinusPlus;",
  "&Mopf;",
  "&Mscr;",
  "&Mu;",
  "&NJcy;",
  "&Nacute;",
  "&Ncaron;",
  "&Ncedil;",
  "&Ncy;",
  "&NegativeMediumSpace;",
  "&NegativeThickSpace;",
  "&NegativeThinSpace;",
  "&NegativeVeryThinSpace;",
  "&NestedGreaterGreater;",
  "&NestedLessLess;",
  "&NewLine;",
  "&Nfr;",
  "&NoBreak;",
  "&NonBreakingSpace;",
  "&Nopf;",
  "&Not;",
  "&NotCongruent;",
  "&NotCupCap;",
  "&NotDoubleVerticalBar;",
  "&NotElement;",
  "&NotEqual;",
  "&NotEqualTilde;",
  "&NotExists;",
  "&NotGreater;",
  "&NotGreaterEqual;",
  "&NotGreaterFullEqual;",
  "&NotGreaterGreater;",
  "&NotGreaterLess;",
  "&NotGreaterSlantEqual;",
  "&NotGreaterTilde;",
  "&NotHumpDownHump;",
  "&NotHumpEqual;",
  "&NotLeftTriangle;",
  "&NotLeftTriangleBar;",
  "&NotLeftTriangleEqual;",
  "&NotLess;",
  "&NotLessEqual;",
  "&NotLessGreater;",
  "&NotLessLess;",
  "&NotLessSlantEqual;",
  "&NotLessTilde;",
  "&NotNestedGreaterGreater;",
  "&NotNestedLessLess;",
  "&NotPrecedes;",
  "&NotPrecedesEqual;",
  "&NotPrecedesSlantEqual;",
  "&NotReverseElement;",
  "&NotRightTriangle;",
  "&NotRightTriangleBar;",
  "&NotRightTriangleEqual;",
  "&NotSquareSubset;",
  "&NotSquareSubsetEqual;",
  "&NotSquareSuperset;",
  "&NotSquareSupersetEqual;",
  "&NotSubset;",
  "&NotSubsetEqual;",
  "&NotSucceeds;",
  "&NotSucceedsEqual;",
  "&NotSucceedsSlantEqual;",
  "&NotSucceedsTilde;",
  "&NotSuperset;",
  "&NotSupersetEqual;",
  "&NotTilde;",
  "&NotTildeEqual;",
  "&NotTildeFullEqual;",
  "&NotTildeTilde;",
  "&NotVerticalBar;",
  "&Nscr;",
  "&Ntilde",
  "&Ntilde;",
  "&Nu;",
  "&OElig;",
  "&Oacute",
  "&Oacute;",
  "&Ocirc",
  "&Ocirc;",
  "&Ocy;",
  "&Odblac;",
  "&Ofr;",
  "&Ograve",
  "&Ograve;",
  "&Omacr;",
  "&Omega;",
  "&Omicron;",
  "&Oopf;",
  "&OpenCurlyDoubleQuote;",
  "&OpenCurlyQuote;",
  "&Or;",
  "&Oscr;",
  "&Oslash",
  "&Oslash;",
  "&Otilde",
  "&Otilde;",
  "&Otimes;",
  "&Ouml",
  "&Ouml;",
  "&OverBar;",
  "&OverBrace;",
  "&OverBracket;",
  "&OverParenthesis;",
  "&PartialD;",
  "&Pcy;",
  "&Pfr;",
  "&Phi;",
  "&Pi;",
  "&PlusMinus;",
  "&Poincareplane;",
  "&Popf;",
  "&Pr;",
  "&Precedes;",
  "&PrecedesEqual;",
  "&PrecedesSlantEqual;",
  "&PrecedesTilde;",
  "&Prime;",
  "&Product;",
  "&Proportion;",
  "&Proportional;",
  "&Pscr;",
  "&Psi;",
  "&QUOT",
  "&QUOT;",
  "&Qfr;",
  "&Qopf;",
  "&Qscr;",
  "&RBarr;",
  "&REG",
  "&REG;",
  "&Racute;",
  "&Rang;",
  "&Rarr;",
  "&Rarrtl;",
  "&Rcaron;",
  "&Rcedil;",
  "&Rcy;",
  "&Re;",
  "&ReverseElement;",
  "&ReverseEquilibrium;",
  "&ReverseUpEquilibrium;",
  "&Rfr;",
  "&Rho;",
  "&RightAngleBracket;",
  "&RightArrow;",
  "&RightArrowBar;",
  "&RightArrowLeftArrow;",
  "&RightCeiling;",
  "&RightDoubleBracket;",
  "&RightDownTeeVector;",
  "&RightDownVector;",
  "&RightDownVectorBar;",
  "&RightFloor;",
  "&RightTee;",
  "&RightTeeArrow;",
  "&RightTeeVector;",
  "&RightTriangle;",
  "&RightTriangleBar;",
  "&RightTriangleEqual;",
  "&RightUpDownVector;",
  "&RightUpTeeVector;",
  "&RightUpVector;",
  "&RightUpVectorBar;",
  "&RightVector;",
  "&RightVectorBar;",
  "&Rightarrow;",
  "&Ropf;",
  "&RoundImplies;",
  "&Rrightarrow;",
  "&Rscr;",
  "&Rsh;",
  "&RuleDelayed;",
  "&SHCHcy;",
  "&SHcy;",
  "&SOFTcy;",
  "&Sacute;",
  "&Sc;",
  "&Scaron;",
  "&Scedil;",
  "&Scirc;",
  "&Scy;",
  "&Sfr;",
  "&ShortDownArrow;",
  "&ShortLeftArrow;",
  "&ShortRightArrow;",
  "&ShortUpArrow;",
  "&Sigma;",
  "&SmallCircle;",
  "&Sopf;",
  "&Sqrt;",
  "&Square;",
  "&SquareIntersection;",
  "&SquareSubset;",
  "&SquareSubsetEqual;",
  "&SquareSuperset;",
  "&SquareSupersetEqual;",
  "&SquareUnion;",
  "&Sscr;",
  "&Star;",
  "&Sub;",
  "&Subset;",
  "&SubsetEqual;",
  "&Succeeds;",
  "&SucceedsEqual;",
  "&SucceedsSlantEqual;",
  "&SucceedsTilde;",
  "&SuchThat;",
  "&Sum;",
  "&Sup;",
  "&Superset;",
  "&SupersetEqual;",
  "&Supset;",
  "&THORN",
  "&THORN;",
  "&TRADE;",
  "&TSHcy;",
  "&TScy;",
  "&Tab;",
  "&Tau;",
  "&Tcaron;",
  "&Tcedil;",
  "&Tcy;",
  "&Tfr;",
  "&Therefore;",
  "&Theta;",
  "&ThickSpace;",
  "&ThinSpace;",
  "&Tilde;",
  "&TildeEqual;",
  "&TildeFullEqual;",
  "&TildeTilde;",
  "&Topf;",
  "&TripleDot;",
  "&Tscr;",
  "&Tstrok;",
  "&Uacute",
  "&Uacute;",
  "&Uarr;",
  "&Uarrocir;",
  "&Ubrcy;",
  "&Ubreve;",
  "&Ucirc",
  "&Ucirc;",
  "&Ucy;",
  "&Udblac;",
  "&Ufr;",
  "&Ugrave",
  "&Ugrave;",
  "&Umacr;",
  "&UnderBar;",
  "&UnderBrace;",
  "&UnderBracket;",
  "&UnderParenthesis;",
  "&Union;",
  "&UnionPlus;",
  "&Uogon;",
  "&Uopf;",
  "&UpArrow;",
  "&UpArrowBar;",
  "&UpArrowDownArrow;",
  "&UpDownArrow;",
  "&UpEquilibrium;",
  "&UpTee;",
  "&UpTeeArrow;",
  "&Uparrow;",
  "&Updownarrow;",
  "&UpperLeftArrow;",
  "&UpperRightArrow;",
  "&Upsi;",
  "&Upsilon;",
  "&Uring;",
  "&Uscr;",
  "&Utilde;",
  "&Uuml",
  "&Uuml;",
  "&VDash;",
  "&Vbar;",
  "&Vcy;",
  "&Vdash;",
  "&Vdashl;",
  "&Vee;",
  "&Verbar;",
  "&Vert;",
  "&VerticalBar;",
  "&VerticalLine;",
  "&VerticalSeparator;",
  "&VerticalTilde;",
  "&VeryThinSpace;",
  "&Vfr;",
  "&Vopf;",
  "&Vscr;",
  "&Vvdash;",
  "&Wcirc;",
  "&Wedge;",
  "&Wfr;",
  "&Wopf;",
  "&Wscr;",
  "&Xfr;",
  "&Xi;",
  "&Xopf;",
  "&Xscr;",
  "&YAcy;",
  "&YIcy;",
  "&YUcy;",
  "&Yacute",
  "&Yacute;",
  "&Ycirc;",
  "&Ycy;",
  "&Yfr;",
  "&Yopf;",
  "&Yscr;",
  "&Yuml;",
  "&ZHcy;",
  "&Zacute;",
  "&Zcaron;",
  "&Zcy;",
  "&Zdot;",
  "&ZeroWidthSpace;",
  "&Zeta;",
  "&Zfr;",
  "&Zopf;",
  "&Zscr;",
  "&aacute",
  "&aacute;",
  "&abreve;",
  "&ac;",
  "&acE;",
  "&acd;",
  "&acirc",
  "&acirc;",
  "&acute",
  "&acute;",
  "&acy;",
  "&aelig",
  "&aelig;",
  "&af;",
  "&afr;",
  "&agrave",
  "&agrave;",
  "&alefsym;",
  "&aleph;",
  "&alpha;",
  "&amacr;",
  "&amalg;",
  "&amp",
  "&amp;",
  "&and;",
  "&andand;",
  "&andd;",
  "&andslope;",
  "&andv;",
  "&ang;",
  "&ange;",
  "&angle;",
  "&angmsd;",
  "&angmsdaa;",
  "&angmsdab;",
  "&angmsdac;",
  "&angmsdad;",
  "&angmsdae;",
  "&angmsdaf;",
  "&angmsdag;",
  "&angmsdah;",
  "&angrt;",
  "&angrtvb;",
  "&angrtvbd;",
  "&angsph;",
  "&angst;",
  "&angzarr;",
  "&aogon;",
  "&aopf;",
  "&ap;",
  "&apE;",
  "&apacir;",
  "&ape;",
  "&apid;",
  "&apos;",
  "&approx;",
  "&approxeq;",
  "&aring",
  "&aring;",
  "&ascr;",
  "&ast;",
  "&asymp;",
  "&asympeq;",
  "&atilde",
  "&atilde;",
  "&auml",
  "&auml;",
  "&awconint;",
  "&awint;",
  "&bNot;",
  "&backcong;",
  "&backepsilon;",
  "&backprime;",
  "&backsim;",
  "&backsimeq;",
  "&barvee;",
  "&barwed;",
  "&barwedge;",
  "&bbrk;",
  "&bbrktbrk;",
  "&bcong;",
  "&bcy;",
  "&bdquo;",
  "&becaus;",
  "&because;",
  "&bemptyv;",
  "&bepsi;",
  "&bernou;",
  "&beta;",
  "&beth;",
  "&between;",
  "&bfr;",
  "&bigcap;",
  "&bigcirc;",
  "&bigcup;",
  "&bigodot;",
  "&bigoplus;",
  "&bigotimes;",
  "&bigsqcup;",
  "&bigstar;",
  "&bigtriangledown;",
  "&bigtriangleup;",
  "&biguplus;",
  "&bigvee;",
  "&bigwedge;",
  "&bkarow;",
  "&blacklozenge;",
  "&blacksquare;",
  "&blacktriangle;",
  "&blacktriangledown;",
  "&blacktriangleleft;",
  "&blacktriangleright;",
  "&blank;",
  "&blk12;",
  "&blk14;",
  "&blk34;",
  "&block;",
  "&bne;",
  "&bnequiv;",
  "&bnot;",
  "&bopf;",
  "&bot;",
  "&bottom;",
  "&bowtie;",
  "&boxDL;",
  "&boxDR;",
  "&boxDl;",
  "&boxDr;",
  "&boxH;",
  "&boxHD;",
  "&boxHU;",
  "&boxHd;",
  "&boxHu;",
  "&boxUL;",
  "&boxUR;",
  "&boxUl;",
  "&boxUr;",
  "&boxV;",
  "&boxVH;",
  "&boxVL;",
  "&boxVR;",
  "&boxVh;",
  "&boxVl;",
  "&boxVr;",
  "&boxbox;",
  "&boxdL;",
  "&boxdR;",
  "&boxdl;",
  "&boxdr;",
  "&boxh;",
  "&boxhD;",
  "&boxhU;",
  "&boxhd;",
  "&boxhu;",
  "&boxminus;",
  "&boxplus;",
  "&boxtimes;",
  "&boxuL;",
  "&boxuR;",
  "&boxul;",
  "&boxur;",
  "&boxv;",
  "&boxvH;",
  "&boxvL;",
  "&boxvR;",
  "&boxvh;",
  "&boxvl;",
  "&boxvr;",
  "&bprime;",
  "&breve;",
  "&brvbar",
  "&brvbar;",
  "&bscr;",
  "&bsemi;",
  "&bsim;",
  "&bsime;",
  "&bsol;",
  "&bsolb;",
  "&bsolhsub;",
  "&bull;",
  "&bullet;",
  "&bump;",
  "&bumpE;",
  "&bumpe;",
  "&bumpeq;",
  "&cacute;",
  "&cap;",
  "&capand;",
  "&capbrcup;",
  "&capcap;",
  "&capcup;",
  "&capdot;",
  "&caps;",
  "&caret;",
  "&caron;",
  "&ccaps;",
  "&ccaron;",
  "&ccedil",
  "&ccedil;",
  "&ccirc;",
  "&ccups;",
  "&ccupssm;",
  "&cdot;",
  "&cedil",
  "&cedil;",
  "&cemptyv;",
  "&cent",
  "&cent;",
  "&centerdot;",
  "&cfr;",
  "&chcy;",
  "&check;",
  "&checkmark;",
  "&chi;",
  "&cir;",
  "&cirE;",
  "&circ;",
  "&circeq;",
  "&circlearrowleft;",
  "&circlearrowright;",
  "&circledR;",
  "&circledS;",
  "&circledast;",
  "&circledcirc;",
  "&circleddash;",
  "&cire;",
  "&cirfnint;",
  "&cirmid;",
  "&cirscir;",
  "&clubs;",
  "&clubsuit;",
  "&colon;",
  "&colone;",
  "&coloneq;",
  "&comma;",
  "&commat;",
  "&comp;",
  "&compfn;",
  "&complement;",
  "&complexes;",
  "&cong;",
  "&congdot;",
  "&conint;",
  "&copf;",
  "&coprod;",
  "&copy",
  "&copy;",
  "&copysr;",
  "&crarr;",
  "&cross;",
  "&cscr;",
  "&csub;",
  "&csube;",
  "&csup;",
  "&csupe;",
  "&ctdot;",
  "&cudarrl;",
  "&cudarrr;",
  "&cuepr;",
  "&cuesc;",
  "&cularr;",
  "&cularrp;",
  "&cup;",
  "&cupbrcap;",
  "&cupcap;",
  "&cupcup;",
  "&cupdot;",
  "&cupor;",
  "&cups;",
  "&curarr;",
  "&curarrm;",
  "&curlyeqprec;",
  "&curlyeqsucc;",
  "&curlyvee;",
  "&curlywedge;",
  "&curren",
  "&curren;",
  "&curvearrowleft;",
  "&curvearrowright;",
  "&cuvee;",
  "&cuwed;",
  "&cwconint;",
  "&cwint;",
  "&cylcty;",
  "&dArr;",
  "&dHar;",
  "&dagger;",
  "&daleth;",
  "&darr;",
  "&dash;",
  "&dashv;",
  "&dbkarow;",
  "&dblac;",
  "&dcaron;",
  "&dcy;",
  "&dd;",
  "&ddagger;",
  "&ddarr;",
  "&ddotseq;",
  "&deg",
  "&deg;",
  "&delta;",
  "&demptyv;",
  "&dfisht;",
  "&dfr;",
  "&dharl;",
  "&dharr;",
  "&diam;",
  "&diamond;",
  "&diamondsuit;",
  "&diams;",
  "&die;",
  "&digamma;",
  "&disin;",
  "&div;",
  "&divide",
  "&divide;",
  "&divideontimes;",
  "&divonx;",
  "&djcy;",
  "&dlcorn;",
  "&dlcrop;",
  "&dollar;",
  "&dopf;",
  "&dot;",
  "&doteq;",
  "&doteqdot;",
  "&dotminus;",
  "&dotplus;",
  "&dotsquare;",
  "&doublebarwedge;",
  "&downarrow;",
  "&downdownarrows;",
  "&downharpoonleft;",
  "&downharpoonright;",
  "&drbkarow;",
  "&drcorn;",
  "&drcrop;",
  "&dscr;",
  "&dscy;",
  "&dsol;",
  "&dstrok;",
  "&dtdot;",
  "&dtri;",
  "&dtrif;",
  "&duarr;",
  "&duhar;",
  "&dwangle;",
  "&dzcy;",
  "&dzigrarr;",
  "&eDDot;",
  "&eDot;",
  "&eacute",
  "&eacute;",
  "&easter;",
  "&ecaron;",
  "&ecir;",
  "&ecirc",
  "&ecirc;",
  "&ecolon;",
  "&ecy;",
  "&edot;",
  "&ee;",
  "&efDot;",
  "&efr;",
  "&eg;",
  "&egrave",
  "&egrave;",
  "&egs;",
  "&egsdot;",
  "&el;",
  "&elinters;",
  "&ell;",
  "&els;",
  "&elsdot;",
  "&emacr;",
  "&empty;",
  "&emptyset;",
  "&emptyv;",
  "&emsp13;",
  "&emsp14;",
  "&emsp;",
  "&eng;",
  "&ensp;",
  "&eogon;",
  "&eopf;",
  "&epar;",
  "&eparsl;",
  "&eplus;",
  "&epsi;",
  "&epsilon;",
  "&epsiv;",
  "&eqcirc;",
  "&eqcolon;",
  "&eqsim;",
  "&eqslantgtr;",
  "&eqslantless;",
  "&equals;",
  "&equest;",
  "&equiv;",
  "&equivDD;",
  "&eqvparsl;",
  "&erDot;",
  "&erarr;",
  "&escr;",
  "&esdot;",
  "&esim;",
  "&eta;",
  "&eth",
  "&eth;",
  "&euml",
  "&euml;",
  "&euro;",
  "&excl;",
  "&exist;",
  "&expectation;",
  "&exponentiale;",
  "&fallingdotseq;",
  "&fcy;",
  "&female;",
  "&ffilig;",
  "&fflig;",
  "&ffllig;",
  "&ffr;",
  "&filig;",
  "&fjlig;",
  "&flat;",
  "&fllig;",
  "&fltns;",
  "&fnof;",
  "&fopf;",
  "&forall;",
  "&fork;",
  "&forkv;",
  "&fpartint;",
  "&frac12",
  "&frac12;",
  "&frac13;",
  "&frac14",
  "&frac14;",
  "&frac15;",
  "&frac16;",
  "&frac18;",
  "&frac23;",
  "&frac25;",
  "&frac34",
  "&frac34;",
  "&frac35;",
  "&frac38;",
  "&frac45;",
  "&frac56;",
  "&frac58;",
  "&frac78;",
  "&frasl;",
  "&frown;",
  "&fscr;",
  "&gE;",
  "&gEl;",
  "&gacute;",
  "&gamma;",
  "&gammad;",
  "&gap;",
  "&gbreve;",
  "&gcirc;",
  "&gcy;",
  "&gdot;",
  "&ge;",
  "&gel;",
  "&geq;",
  "&geqq;",
  "&geqslant;",
  "&ges;",
  "&gescc;",
  "&gesdot;",
  "&gesdoto;",
  "&gesdotol;",
  "&gesl;",
  "&gesles;",
  "&gfr;",
  "&gg;",
  "&ggg;",
  "&gimel;",
  "&gjcy;",
  "&gl;",
  "&glE;",
  "&gla;",
  "&glj;",
  "&gnE;",
  "&gnap;",
  "&gnapprox;",
  "&gne;",
  "&gneq;",
  "&gneqq;",
  "&gnsim;",
  "&gopf;",
  "&grave;",
  "&gscr;",
  "&gsim;",
  "&gsime;",
  "&gsiml;",
  "&gt",
  "&gt;",
  "&gtcc;",
  "&gtcir;",
  "&gtdot;",
  "&gtlPar;",
  "&gtquest;",
  "&gtrapprox;",
  "&gtrarr;",
  "&gtrdot;",
  "&gtreqless;",
  "&gtreqqless;",
  "&gtrless;",
  "&gtrsim;",
  "&gvertneqq;",
  "&gvnE;",
  "&hArr;",
  "&hairsp;",
  "&half;",
  "&hamilt;",
  "&hardcy;",
  "&harr;",
  "&harrcir;",
  "&harrw;",
  "&hbar;",
  "&hcirc;",
  "&hearts;",
  "&heartsuit;",
  "&hellip;",
  "&hercon;",
  "&hfr;",
  "&hksearow;",
  "&hkswarow;",
  "&hoarr;",
  "&homtht;",
  "&hookleftarrow;",
  "&hookrightarrow;",
  "&hopf;",
  "&horbar;",
  "&hscr;",
  "&hslash;",
  "&hstrok;",
  "&hybull;",
  "&hyphen;",
  "&iacute",
  "&iacute;",
  "&ic;",
  "&icirc",
  "&icirc;",
  "&icy;",
  "&iecy;",
  "&iexcl",
  "&iexcl;",
  "&iff;",
  "&ifr;",
  "&igrave",
  "&igrave;",
  "&ii;",
  "&iiiint;",
  "&iiint;",
  "&iinfin;",
  "&iiota;",
  "&ijlig;",
  "&imacr;",
  "&image;",
  "&imagline;",
  "&imagpart;",
  "&imath;",
  "&imof;",
  "&imped;",
  "&in;",
  "&incare;",
  "&infin;",
  "&infintie;",
  "&inodot;",
  "&int;",
  "&intcal;",
  "&integers;",
  "&intercal;",
  "&intlarhk;",
  "&intprod;",
  "&iocy;",
  "&iogon;",
  "&iopf;",
  "&iota;",
  "&iprod;",
  "&iquest",
  "&iquest;",
  "&iscr;",
  "&isin;",
  "&isinE;",
  "&isindot;",
  "&isins;",
  "&isinsv;",
  "&isinv;",
  "&it;",
  "&itilde;",
  "&iukcy;",
  "&iuml",
  "&iuml;",
  "&jcirc;",
  "&jcy;",
  "&jfr;",
  "&jmath;",
  "&jopf;",
  "&jscr;",
  "&jsercy;",
  "&jukcy;",
  "&kappa;",
  "&kappav;",
  "&kcedil;",
  "&kcy;",
  "&kfr;",
  "&kgreen;",
  "&khcy;",
  "&kjcy;",
  "&kopf;",
  "&kscr;",
  "&lAarr;",
  "&lArr;",
  "&lAtail;",
  "&lBarr;",
  "&lE;",
  "&lEg;",
  "&lHar;",
  "&lacute;",
  "&laemptyv;",
  "&lagran;",
  "&lambda;",
  "&lang;",
  "&langd;",
  "&langle;",
  "&lap;",
  "&laquo",
  "&laquo;",
  "&larr;",
  "&larrb;",
  "&larrbfs;",
  "&larrfs;",
  "&larrhk;",
  "&larrlp;",
  "&larrpl;",
  "&larrsim;",
  "&larrtl;",
  "&lat;",
  "&latail;",
  "&late;",
  "&lates;",
  "&lbarr;",
  "&lbbrk;",
  "&lbrace;",
  "&lbrack;",
  "&lbrke;",
  "&lbrksld;",
  "&lbrkslu;",
  "&lcaron;",
  "&lcedil;",
  "&lceil;",
  "&lcub;",
  "&lcy;",
  "&ldca;",
  "&ldquo;",
  "&ldquor;",
  "&ldrdhar;",
  "&ldrushar;",
  "&ldsh;",
  "&le;",
  "&leftarrow;",
  "&leftarrowtail;",
  "&leftharpoondown;",
  "&leftharpoonup;",
  "&leftleftarrows;",
  "&leftrightarrow;",
  "&leftrightarrows;",
  "&leftrightharpoons;",
  "&leftrightsquigarrow;",
  "&leftthreetimes;",
  "&leg;",
  "&leq;",
  "&leqq;",
  "&leqslant;",
  "&les;",
  "&lescc;",
  "&lesdot;",
  "&lesdoto;",
  "&lesdotor;",
  "&lesg;",
  "&lesges;",
  "&lessapprox;",
  "&lessdot;",
  "&lesseqgtr;",
  "&lesseqqgtr;",
  "&lessgtr;",
  "&lesssim;",
  "&lfisht;",
  "&lfloor;",
  "&lfr;",
  "&lg;",
  "&lgE;",
  "&lhard;",
  "&lharu;",
  "&lharul;",
  "&lhblk;",
  "&ljcy;",
  "&ll;",
  "&llarr;",
  "&llcorner;",
  "&llhard;",
  "&lltri;",
  "&lmidot;",
  "&lmoust;",
  "&lmoustache;",
  "&lnE;",
  "&lnap;",
  "&lnapprox;",
  "&lne;",
  "&lneq;",
  "&lneqq;",
  "&lnsim;",
  "&loang;",
  "&loarr;",
  "&lobrk;",
  "&longleftarrow;",
  "&longleftrightarrow;",
  "&longmapsto;",
  "&longrightarrow;",
  "&looparrowleft;",
  "&looparrowright;",
  "&lopar;",
  "&lopf;",
  "&loplus;",
  "&lotimes;",
  "&lowast;",
  "&lowbar;",
  "&loz;",
  "&lozenge;",
  "&lozf;",
  "&lpar;",
  "&lparlt;",
  "&lrarr;",
  "&lrcorner;",
  "&lrhar;",
  "&lrhard;",
  "&lrm;",
  "&lrtri;",
  "&lsaquo;",
  "&lscr;",
  "&lsh;",
  "&lsim;",
  "&lsime;",
  "&lsimg;",
  "&lsqb;",
  "&lsquo;",
  "&lsquor;",
  "&lstrok;",
  "&lt",
  "&lt;",
  "&ltcc;",
  "&ltcir;",
  "&ltdot;",
  "&lthree;",
  "&ltimes;",
  "&ltlarr;",
  "&ltquest;",
  "&ltrPar;",
  "&ltri;",
  "&ltrie;",
  "&ltrif;",
  "&lurdshar;",
  "&luruhar;",
  "&lvertneqq;",
  "&lvnE;",
  "&mDDot;",
  "&macr",
  "&macr;",
  "&male;",
  "&malt;",
  "&maltese;",
  "&map;",
  "&mapsto;",
  "&mapstodown;",
  "&mapstoleft;",
  "&mapstoup;",
  "&marker;",
  "&mcomma;",
  "&mcy;",
  "&mdash;",
  "&measuredangle;",
  "&mfr;",
  "&mho;",
  "&micro",
  "&micro;",
  "&mid;",
  "&midast;",
  "&midcir;",
  "&middot",
  "&middot;",
  "&minus;",
  "&minusb;",
  "&minusd;",
  "&minusdu;",
  "&mlcp;",
  "&mldr;",
  "&mnplus;",
  "&models;",
  "&mopf;",
  "&mp;",
  "&mscr;",
  "&mstpos;",
  "&mu;",
  "&multimap;",
  "&mumap;",
  "&nGg;",
  "&nGt;",
  "&nGtv;",
  "&nLeftarrow;",
  "&nLeftrightarrow;",
  "&nLl;",
  "&nLt;",
  "&nLtv;",
  "&nRightarrow;",
  "&nVDash;",
  "&nVdash;",
  "&nabla;",
  "&nacute;",
  "&nang;",
  "&nap;",
  "&napE;",
  "&napid;",
  "&napos;",
  "&napprox;",
  "&natur;",
  "&natural;",
  "&naturals;",
  "&nbsp",
  "&nbsp;",
  "&nbump;",
  "&nbumpe;",
  "&ncap;",
  "&ncaron;",
  "&ncedil;",
  "&ncong;",
  "&ncongdot;",
  "&ncup;",
  "&ncy;",
  "&ndash;",
  "&ne;",
  "&neArr;",
  "&nearhk;",
  "&nearr;",
  "&nearrow;",
  "&nedot;",
  "&nequiv;",
  "&nesear;",
  "&nesim;",
  "&nexist;",
  "&nexists;",
  "&nfr;",
  "&ngE;",
  "&nge;",
  "&ngeq;",
  "&ngeqq;",
  "&ngeqslant;",
  "&nges;",
  "&ngsim;",
  "&ngt;",
  "&ngtr;",
  "&nhArr;",
  "&nharr;",
  "&nhpar;",
  "&ni;",
  "&nis;",
  "&nisd;",
  "&niv;",
  "&njcy;",
  "&nlArr;",
  "&nlE;",
  "&nlarr;",
  "&nldr;",
  "&nle;",
  "&nleftarrow;",
  "&nleftrightarrow;",
  "&nleq;",
  "&nleqq;",
  "&nleqslant;",
  "&nles;",
  "&nless;",
  "&nlsim;",
  "&nlt;",
  "&nltri;",
  "&nltrie;",
  "&nmid;",
  "&nopf;",
  "&not",
  "&not;",
  "&notin;",
  "&notinE;",
  "&notindot;",
  "&notinva;",
  "&notinvb;",
  "&notinvc;",
  "&notni;",
  "&notniva;",
  "&notnivb;",
  "&notnivc;",
  "&npar;",
  "&nparallel;",
  "&nparsl;",
  "&npart;",
  "&npolint;",
  "&npr;",
  "&nprcue;",
  "&npre;",
  "&nprec;",
  "&npreceq;",
  "&nrArr;",
  "&nrarr;",
  "&nrarrc;",
  "&nrarrw;",
  "&nrightarrow;",
  "&nrtri;",
  "&nrtrie;",
  "&nsc;",
  "&nsccue;",
  "&nsce;",
  "&nscr;",
  "&nshortmid;",
  "&nshortparallel;",
  "&nsim;",
  "&nsime;",
  "&nsimeq;",
  "&nsmid;",
  "&nspar;",
  "&nsqsube;",
  "&nsqsupe;",
  "&nsub;",
  "&nsubE;",
  "&nsube;",
  "&nsubset;",
  "&nsubseteq;",
  "&nsubseteqq;",
  "&nsucc;",
  "&nsucceq;",
  "&nsup;",
  "&nsupE;",
  "&nsupe;",
  "&nsupset;",
  "&nsupseteq;",
  "&nsupseteqq;",
  "&ntgl;",
  "&ntilde",
  "&ntilde;",
  "&ntlg;",
  "&ntriangleleft;",
  "&ntrianglelefteq;",
  "&ntriangleright;",
  "&ntrianglerighteq;",
  "&nu;",
  "&num;",
  "&numero;",
  "&numsp;",
  "&nvDash;",
  "&nvHarr;",
  "&nvap;",
  "&nvdash;",
  "&nvge;",
  "&nvgt;",
  "&nvinfin;",
  "&nvlArr;",
  "&nvle;",
  "&nvlt;",
  "&nvltrie;",
  "&nvrArr;",
  "&nvrtrie;",
  "&nvsim;",
  "&nwArr;",
  "&nwarhk;",
  "&nwarr;",
  "&nwarrow;",
  "&nwnear;",
  "&oS;",
  "&oacute",
  "&oacute;",
  "&oast;",
  "&ocir;",
  "&ocirc",
  "&ocirc;",
  "&ocy;",
  "&odash;",
  "&odblac;",
  "&odiv;",
  "&odot;",
  "&odsold;",
  "&oelig;",
  "&ofcir;",
  "&ofr;",
  "&ogon;",
  "&ograve",
  "&ograve;",
  "&ogt;",
  "&ohbar;",
  "&ohm;",
  "&oint;",
  "&olarr;",
  "&olcir;",
  "&olcross;",
  "&oline;",
  "&olt;",
  "&omacr;",
  "&omega;",
  "&omicron;",
  "&omid;",
  "&ominus;",
  "&oopf;",
  "&opar;",
  "&operp;",
  "&oplus;",
  "&or;",
  "&orarr;",
  "&ord;",
  "&order;",
  "&orderof;",
  "&ordf",
  "&ordf;",
  "&ordm",
  "&ordm;",
  "&origof;",
  "&oror;",
  "&orslope;",
  "&orv;",
  "&oscr;",
  "&oslash",
  "&oslash;",
  "&osol;",
  "&otilde",
  "&otilde;",
  "&otimes;",
  "&otimesas;",
  "&ouml",
  "&ouml;",
  "&ovbar;",
  "&par;",
  "&para",
  "&para;",
  "&parallel;",
  "&parsim;",
  "&parsl;",
  "&part;",
  "&pcy;",
  "&percnt;",
  "&period;",
  "&permil;",
  "&perp;",
  "&pertenk;",
  "&pfr;",
  "&phi;",
  "&phiv;",
  "&phmmat;",
  "&phone;",
  "&pi;",
  "&pitchfork;",
  "&piv;",
  "&planck;",
  "&planckh;",
  "&plankv;",
  "&plus;",
  "&plusacir;",
  "&plusb;",
  "&pluscir;",
  "&plusdo;",
  "&plusdu;",
  "&pluse;",
  "&plusmn",
  "&plusmn;",
  "&plussim;",
  "&plustwo;",
  "&pm;",
  "&pointint;",
  "&popf;",
  "&pound",
  "&pound;",
  "&pr;",
  "&prE;",
  "&prap;",
  "&prcue;",
  "&pre;",
  "&prec;",
  "&precapprox;",
  "&preccurlyeq;",
  "&preceq;",
  "&precnapprox;",
  "&precneqq;",
  "&precnsim;",
  "&precsim;",
  "&prime;",
  "&primes;",
  "&prnE;",
  "&prnap;",
  "&prnsim;",
  "&prod;",
  "&profalar;",
  "&profline;",
  "&profsurf;",
  "&prop;",
  "&propto;",
  "&prsim;",
  "&prurel;",
  "&pscr;",
  "&psi;",
  "&puncsp;",
  "&qfr;",
  "&qint;",
  "&qopf;",
  "&qprime;",
  "&qscr;",
  "&quaternions;",
  "&quatint;",
  "&quest;",
  "&questeq;",
  "&quot",
  "&quot;",
  "&rAarr;",
  "&rArr;",
  "&rAtail;",
  "&rBarr;",
  "&rHar;",
  "&race;",
  "&racute;",
  "&radic;",
  "&raemptyv;",
  "&rang;",
  "&rangd;",
  "&range;",
  "&rangle;",
  "&raquo",
  "&raquo;",
  "&rarr;",
  "&rarrap;",
  "&rarrb;",
  "&rarrbfs;",
  "&rarrc;",
  "&rarrfs;",
  "&rarrhk;",
  "&rarrlp;",
  "&rarrpl;",
  "&rarrsim;",
  "&rarrtl;",
  "&rarrw;",
  "&ratail;",
  "&ratio;",
  "&rationals;",
  "&rbarr;",
  "&rbbrk;",
  "&rbrace;",
  "&rbrack;",
  "&rbrke;",
  "&rbrksld;",
  "&rbrkslu;",
  "&rcaron;",
  "&rcedil;",
  "&rceil;",
  "&rcub;",
  "&rcy;",
  "&rdca;",
  "&rdldhar;",
  "&rdquo;",
  "&rdquor;",
  "&rdsh;",
  "&real;",
  "&realine;",
  "&realpart;",
  "&reals;",
  "&rect;",
  "&reg",
  "&reg;",
  "&rfisht;",
  "&rfloor;",
  "&rfr;",
  "&rhard;",
  "&rharu;",
  "&rharul;",
  "&rho;",
  "&rhov;",
  "&rightarrow;",
  "&rightarrowtail;",
  "&rightharpoondown;",
  "&rightharpoonup;",
  "&rightleftarrows;",
  "&rightleftharpoons;",
  "&rightrightarrows;",
  "&rightsquigarrow;",
  "&rightthreetimes;",
  "&ring;",
  "&risingdotseq;",
  "&rlarr;",
  "&rlhar;",
  "&rlm;",
  "&rmoust;",
  "&rmoustache;",
  "&rnmid;",
  "&roang;",
  "&roarr;",
  "&robrk;",
  "&ropar;",
  "&ropf;",
  "&roplus;",
  "&rotimes;",
  "&rpar;",
  "&rpargt;",
  "&rppolint;",
  "&rrarr;",
  "&rsaquo;",
  "&rscr;",
  "&rsh;",
  "&rsqb;",
  "&rsquo;",
  "&rsquor;",
  "&rthree;",
  "&rtimes;",
  "&rtri;",
  "&rtrie;",
  "&rtrif;",
  "&rtriltri;",
  "&ruluhar;",
  "&rx;",
  "&sacute;",
  "&sbquo;",
  "&sc;",
  "&scE;",
  "&scap;",
  "&scaron;",
  "&sccue;",
  "&sce;",
  "&scedil;",
  "&scirc;",
  "&scnE;",
  "&scnap;",
  "&scnsim;",
  "&scpolint;",
  "&scsim;",
  "&scy;",
  "&sdot;",
  "&sdotb;",
  "&sdote;",
  "&seArr;",
  "&searhk;",
  "&searr;",
  "&searrow;",
  "&sect",
  "&sect;",
  "&semi;",
  "&seswar;",
  "&setminus;",
  "&setmn;",
  "&sext;",
  "&sfr;",
  "&sfrown;",
  "&sharp;",
  "&shchcy;",
  "&shcy;",
  "&shortmid;",
  "&shortparallel;",
  "&shy",
  "&shy;",
  "&sigma;",
  "&sigmaf;",
  "&sigmav;",
  "&sim;",
  "&simdot;",
  "&sime;",
  "&simeq;",
  "&simg;",
  "&simgE;",
  "&siml;",
  "&simlE;",
  "&simne;",
  "&simplus;",
  "&simrarr;",
  "&slarr;",
  "&smallsetminus;",
  "&smashp;",
  "&smeparsl;",
  "&smid;",
  "&smile;",
  "&smt;",
  "&smte;",
  "&smtes;",
  "&softcy;",
  "&sol;",
  "&solb;",
  "&solbar;",
  "&sopf;",
  "&spades;",
  "&spadesuit;",
  "&spar;",
  "&sqcap;",
  "&sqcaps;",
  "&sqcup;",
  "&sqcups;",
  "&sqsub;",
  "&sqsube;",
  "&sqsubset;",
  "&sqsubseteq;",
  "&sqsup;",
  "&sqsupe;",
  "&sqsupset;",
  "&sqsupseteq;",
  "&squ;",
  "&square;",
  "&squarf;",
  "&squf;",
  "&srarr;",
  "&sscr;",
  "&ssetmn;",
  "&ssmile;",
  "&sstarf;",
  "&star;",
  "&starf;",
  "&straightepsilon;",
  "&straightphi;",
  "&strns;",
  "&sub;",
  "&subE;",
  "&subdot;",
  "&sube;",
  "&subedot;",
  "&submult;",
  "&subnE;",
  "&subne;",
  "&subplus;",
  "&subrarr;",
  "&subset;",
  "&subseteq;",
  "&subseteqq;",
  "&subsetneq;",
  "&subsetneqq;",
  "&subsim;",
  "&subsub;",
  "&subsup;",
  "&succ;",
  "&succapprox;",
  "&succcurlyeq;",
  "&succeq;",
  "&succnapprox;",
  "&succneqq;",
  "&succnsim;",
  "&succsim;",
  "&sum;",
  "&sung;",
  "&sup1",
  "&sup1;",
  "&sup2",
  "&sup2;",
  "&sup3",
  "&sup3;",
  "&sup;",
  "&supE;",
  "&supdot;",
  "&supdsub;",
  "&supe;",
  "&supedot;",
  "&suphsol;",
  "&suphsub;",
  "&suplarr;",
  "&supmult;",
  "&supnE;",
  "&supne;",
  "&supplus;",
  "&supset;",
  "&supseteq;",
  "&supseteqq;",
  "&supsetneq;",
  "&supsetneqq;",
  "&supsim;",
  "&supsub;",
  "&supsup;",
  "&swArr;",
  "&swarhk;",
  "&swarr;",
  "&swarrow;",
  "&swnwar;",
  "&szlig",
  "&szlig;",
  "&target;",
  "&tau;",
  "&tbrk;",
  "&tcaron;",
  "&tcedil;",
  "&tcy;",
  "&tdot;",
  "&telrec;",
  "&tfr;",
  "&there4;",
  "&therefore;",
  "&theta;",
  "&thetasym;",
  "&thetav;",
  "&thickapprox;",
  "&thicksim;",
  "&thinsp;",
  "&thkap;",
  "&thksim;",
  "&thorn",
  "&thorn;",
  "&tilde;",
  "&times",
  "&times;",
  "&timesb;",
  "&timesbar;",
  "&timesd;",
  "&tint;",
  "&toea;",
  "&top;",
  "&topbot;",
  "&topcir;",
  "&topf;",
  "&topfork;",
  "&tosa;",
  "&tprime;",
  "&trade;",
  "&triangle;",
  "&triangledown;",
  "&triangleleft;",
  "&trianglelefteq;",
  "&triangleq;",
  "&triangleright;",
  "&trianglerighteq;",
  "&tridot;",
  "&trie;",
  "&triminus;",
  "&triplus;",
  "&trisb;",
  "&tritime;",
  "&trpezium;",
  "&tscr;",
  "&tscy;",
  "&tshcy;",
  "&tstrok;",
  "&twixt;",
  "&twoheadleftarrow;",
  "&twoheadrightarrow;",
  "&uArr;",
  "&uHar;",
  "&uacute",
  "&uacute;",
  "&uarr;",
  "&ubrcy;",
  "&ubreve;",
  "&ucirc",
  "&ucirc;",
  "&ucy;",
  "&udarr;",
  "&udblac;",
  "&udhar;",
  "&ufisht;",
  "&ufr;",
  "&ugrave",
  "&ugrave;",
  "&uharl;",
  "&uharr;",
  "&uhblk;",
  "&ulcorn;",
  "&ulcorner;",
  "&ulcrop;",
  "&ultri;",
  "&umacr;",
  "&uml",
  "&uml;",
  "&uogon;",
  "&uopf;",
  "&uparrow;",
  "&updownarrow;",
  "&upharpoonleft;",
  "&upharpoonright;",
  "&uplus;",
  "&upsi;",
  "&upsih;",
  "&upsilon;",
  "&upuparrows;",
  "&urcorn;",
  "&urcorner;",
  "&urcrop;",
  "&uring;",
  "&urtri;",
  "&uscr;",
  "&utdot;",
  "&utilde;",
  "&utri;",
  "&utrif;",
  "&uuarr;",
  "&uuml",
  "&uuml;",
  "&uwangle;",
  "&vArr;",
  "&vBar;",
  "&vBarv;",
  "&vDash;",
  "&vangrt;",
  "&varepsilon;",
  "&varkappa;",
  "&varnothing;",
  "&varphi;",
  "&varpi;",
  "&varpropto;",
  "&varr;",
  "&varrho;",
  "&varsigma;",
  "&varsubsetneq;",
  "&varsubsetneqq;",
  "&varsupsetneq;",
  "&varsupsetneqq;",
  "&vartheta;",
  "&vartriangleleft;",
  "&vartriangleright;",
  "&vcy;",
  "&vdash;",
  "&vee;",
  "&veebar;",
  "&veeeq;",
  "&vellip;",
  "&verbar;",
  "&vert;",
  "&vfr;",
  "&vltri;",
  "&vnsub;",
  "&vnsup;",
  "&vopf;",
  "&vprop;",
  "&vrtri;",
  "&vscr;",
  "&vsubnE;",
  "&vsubne;",
  "&vsupnE;",
  "&vsupne;",
  "&vzigzag;",
  "&wcirc;",
  "&wedbar;",
  "&wedge;",
  "&wedgeq;",
  "&weierp;",
  "&wfr;",
  "&wopf;",
  "&wp;",
  "&wr;",
  "&wreath;",
  "&wscr;",
  "&xcap;",
  "&xcirc;",
  "&xcup;",
  "&xdtri;",
  "&xfr;",
  "&xhArr;",
  "&xharr;",
  "&xi;",
  "&xlArr;",
  "&xlarr;",
  "&xmap;",
  "&xnis;",
  "&xodot;",
  "&xopf;",
  "&xoplus;",
  "&xotime;",
  "&xrArr;",
  "&xrarr;",
  "&xscr;",
  "&xsqcup;",
  "&xuplus;",
  "&xutri;",
  "&xvee;",
  "&xwedge;",
  "&yacute",
  "&yacute;",
  "&yacy;",
  "&ycirc;",
  "&ycy;",
  "&yen",
  "&yen;",
  "&yfr;",
  "&yicy;",
  "&yopf;",
  "&yscr;",
  "&yucy;",
  "&yuml",
  "&yuml;",
  "&zacute;",
  "&zcaron;",
  "&zcy;",
  "&zdot;",
  "&zeetrf;",
  "&zeta;",
  "&zfr;",
  "&zhcy;",
  "&zigrarr;",
  "&zopf;",
  "&zscr;",
  "&zwj;",
  "&zwnj;"
]);