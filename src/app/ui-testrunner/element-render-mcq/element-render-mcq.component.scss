@import './../element-render/element-render.component.scss';

$selectedColor: #209cee;
$backColor: #F1F1F1;
$borRadius: 0.5em;
.small-instruction {
    padding: 1em;
    text-align: center;
    font-style: italic;
    font-size: 80%;
}

.is-locked {
    pointer-events: none;
}

.drop-down-invert {
    color: white !important;
    border-color: white !important;
    &.an-option {
        background-color: #333 !important
    }
    &.not-option {
        background-color: black !important;
        border-width: 2px !important;
    }
}

.custom-dropdown {
    .custom-dropdown-button {
        font: inherit;
        min-width:3em;
        min-height:2em;
        border: 1px solid $selectedColor;
        border-radius:0.2em;
        i {
            color: $selectedColor
        }
        cursor: pointer;
        background-color: white;
    }
    .dropdown-option-container {
        position:absolute;
        min-width:3em;
        &.droplist-opened{
            box-shadow: 0 0.3em 1em 0em rgba(0, 0, 0, 0.25);
        }
        .dropdown-option {
            display:flex;
            justify-content:center;
            align-items: center;
            border-bottom: 1px solid rgba(0,0,0,0.2);
            padding:0.1em 0.5em;
            min-height: 1.2em;
            background-color: white;
            &.droplist-closed {
                font-size:0em;
                width: 0em;
                height: 0em;
                max-width:0em;
                max-height:0em;
                min-width:0em;
                min-height:0em;
            }
        }
        .dropdown-option:hover {
            background: linear-gradient(rgb(113, 162, 235), rgb(42, 109, 253));
            color:white;
            cursor: pointer;
        }
    }
}

.option-container {
    display:flex;
    // min-width: 300px;
    // width: fit-content;
    overflow: visible;
    position:relative;
    .option-link {
        flex-grow: 1;
        background-color: $backColor;
        border-left-style: dashed;
        border-color: #dddddd;
        margin-left: 0.5em;
        padding-left: 0.5em;
        padding-right: 0.5em;
        border-radius: $borRadius;
    }
    &.is-absolute-children {
        .option-button-container {
            position:absolute;
        }
    }
    .option-button-container {
        display: flex;
    }
    &.has-frame {
        border: 1px solid #ccc;
    }
    &.is-no-option-indicator {
        button.option {
            padding-left: 0.5em;
            .option-indicator {
                display:none;
            }
        }
    }
    .option {
        position: relative;
        font-size: 1em;
        // width: 100%;
        // min-height: 2em;
        background-color: $backColor;
        margin: 0.2em 0.2em;
        border-radius: $borRadius;
        user-select: none;
        cursor: pointer;
        display: flex;
        justify-content: flex-start;
        flex-direction: row;
        align-items: stretch; // center;
        background-color: $backColor;
        border: 2px solid #ccc;
     
        transition: 150ms;
        box-sizing: border-box;
        overflow: visible;
        text-align: left;
        font-family: 'Source Sans 3', sans-serif;

        &:not(.is-simple) {
            &:focus:not(:active){
                 outline: none;
                 box-shadow: 0 0 0 0.125em rgb(0, 0, 0);
                 border-color: transparent;
             }
             &:active {
                 outline: none;
                 box-shadow: 0 0 0 0.125em $selectedColor;
                 border-color: transparent;
            }
        }

        .hard-selection {
            position:absolute;
            top: 0em;
            bottom: 0em;
            left: 0em;
            right: 0em;
            display: none;
        }
        .option-content {
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            overflow: visible;
        }
        &.is-contents-vert-pad {
            padding-top:0.6em;
            padding-bottom:0.6em;
            .option-content {
                margin-left: 0.4em;
                &.is-option-content-centered {
                    margin-right: 0.4em;
                }
            }
        }
        &.is-polaroid-style {
            box-shadow: 0px 3px 10px rgba(0,0,0,0.6);
            border: 2px solid #000;
            background-color: #fff;
            padding:0em;
            border-radius: 0em;
            .option-content {
                padding:0em;
                margin:0em;
                overflow: hidden;
            }
        }
        
        &.is-bg-clear {
            border: 2px solid rgba(0,0,0,0);
            background-color: unset;
        }
        &.is-centered {
            justify-content: center;
        }
        
        &.is-text-aligned-center {
            text-align: center;
        }
        &.is-contents-justified-center {
            justify-content: center;
           /* .option-content {
                justify-content: center;
                align-items: center;
            }*/
        }
        &.is-contents-aligned-center {
            align-items: center;
        }
        &.is-borderless {
            border: 2px solid rgba(0,0,0,0);
        }
        .option-indicator {
            $circleLength: 1.5em;
            // border-top-left-radius: 0.7em;
            // border-bottom-left-radius: 0.7em;
            background-color: #dbdbdb;
            position: absolute;
            top: 0px;
            bottom: 0px;
            // left:0px;
            width: 4em;
            display: flex;
            flex-direction: row;
            justify-content: space-around;
            align-items: flex-start;
            font-size:0.7em;
            padding: 0.5em;
            border-top-left-radius: 0.6em;
            border-bottom-left-radius: 0.6em;
            &.no-bg {
                background-color: transparent;
            }
            .radio-label {
                font-weight: bold;
                line-height: 1em;
            }
            .radio-container {
                box-sizing: border-box;
                position: relative;
                width: $circleLength;
                height: $circleLength;
                flex-shrink: 0;
                .radio-outer, .radio-inner {
                    box-sizing: border-box;
                    height: $circleLength;
                    left: 0;
                    position: absolute;
                    top: 0;
                    width: $circleLength;
                    border-radius: 50%;
                }
                .radio-outer {
                    border-color: #333;
                    border-width: 2px;
                    border-style: solid;
                }
                .radio-inner {
                    opacity:0;
                    background-color: #333;
                    transform: scale(0.001);
                    transition: transform ease 280ms,opacity ease 280ms;
                }
                .checkmark {
                    background-color: rgba(0,0,0,0);
                }
                .is-square {
                    border-radius: 0%;
                }
            }
        }
        
        &.is-simple {
            &:hover {
                box-shadow: 0 0 0 0.075em #999;
            }
        }

        &:not(.is-simple) {
            &:hover:not(:active) {
                border: 2px solid #999;
            }
        }

        &.is-custom-hover-effect{
            &:hover {
                transform: scale(1.2);
                background-color: white;
            }
        }
        
        &.is-active {
            &:not(.is-simple) {
                border: 2px solid $selectedColor;
                box-shadow: 0px 3px 10px rgba(0,0,0,0.2);
                &:focus {
                    border-color: transparent;
                    box-shadow: 0 0 0 0.125em $selectedColor;
                }
            }

            &.is-simple {
                box-shadow: 0 0 0 0.075em $selectedColor;
                &:hover {
                    box-shadow: 0 0 0 0.1em $selectedColor;
                }
            }
            background-color:#fff;
            z-index: 999;
            &.is-bg-clear {
                box-shadow: 0px 3px 20px rgba(0,0,0,0.4);
                &.is-bg-clear-always {
                    background-color:rgba(0,0,0,0);
                }
            }
            &.is-selected-not-obvious{
                border: none;
                box-shadow: none;
            }
            &.is-polaroid-style {
                border: 2px solid rgba(0,0,0,0);
                box-shadow: 0px 3px 20px rgba(0,0,0,0.6);
            }
            .hard-selection.is-enabled {
                display: block;
                border:4px solid #0000ff;
                $defaultPos:-2px;
                $offsetPos:-8px;
                top: $defaultPos; bottom: $defaultPos; left: $defaultPos; right: $defaultPos;
                &.is-offset {
                    top: $offsetPos; bottom: $offsetPos; left: $offsetPos; right: $offsetPos;
                }
            }

            .option-indicator {
                background-color: #a5d1f1;
                &.no-bg {
                    background-color: transparent;
                }
                .radio-container {
                    .radio-inner{
                        opacity: 1;
                        transform: scale(0.5);
                    }
                    .checkmark {
                        transform: scale(0.75);
                    }
                }
            }
            .option-link {
                background-color:#fff;
            }
            &.is-incorrect {
                border: 2px solid #e6bc4b;
                .option-indicator {
                    background-color: #e7d192;
                }
            }
            &.is-correct {
                border: 2px solid #7ac57a;
                .option-indicator {
                    background-color: #ccf0cc;
                }
            }
        }
        &.is-missed {
            border: 2px solid #00b159;
        }

    }
    &.is-vertical {
        flex-direction: column;
        // align-items: flex-start;
        button.option {
            display: flex;
            flex-grow: 1;
            flex-direction: column;
            .option { 
                flex-grow: 1;
                min-width: 300px;
                min-height: 2em;
            }
        }
        &.is-limitted-width {
            button.option {
                max-width: 480px; // this is really just for the sample test
                white-space: pre-wrap;
            }
        }
    }
    &.is-horizontal {
        flex-direction: row;
        .option {
            flex-grow: 1 ;
        }
    }
    &.is-grid {
        flex-wrap: wrap;
        button.option {
            flex-grow: 0;
        }
    }
    &.is-wraparound {
        flex-wrap: wrap;
        justify-content: center;
        .option { 
            // flex-grow: 1 ;
            padding: 0.2em 0.6em;
        }
    }
    
}

// @keyframes inner-scaleup {
// 	0% {
// 		transform: scale(0.001);
// 	}
// 	100% {
// 		transform: scale(0.5);
// 	}
// }

.adv-text-container {
    white-space: pre-wrap;
    line-height:1.2em
}

.likert-buttons {
    margin-top: 1em;
    margin-bottom: 1em;
    // position: relative;
    margin-right: 2em;
    margin-left: 2em;
    font-size:0.7em;
    .button {
        margin-right:0em;
        min-width: 4em;
        position:relative;
        span { margin: 0em 1em; }
    }
    .tail-arrow { display: none; }
    &.is-colored {
        .button {
            &.option-1 {background-color: #ed2826}
            &.option-2 {background-color: #af2124}
            &.option-3 {background-color: #a7a7a7}
            &.option-4 {background-color: #216131}
            &.option-5 {background-color: #46ae3d}
            .tail-arrow { display: block; }
            .radio-icon {
                background-color: #fff;
                border-radius: 100%;
            }
            span { color: #fff; }
        }
    }
}

// .select {
//     position: relative;
//     top: -0.5em;
// }
.select-dropdown {
    vertical-align: baseline !important;
    .dropdown-validate{
        font-family: 'Source Sans 3' !important;
        &.is-correct {
            border: 2px solid #7ac57a !important;
        }
        &.is-incorrect {
            border: 2px solid #e6bc4b !important;
        }
        &.has-highlight {
          border: 5px solid  #ffdd57;
        }
        &.has-highlight-resolved {
          border: 5px solid #23d160;
        }
    }
        
}
