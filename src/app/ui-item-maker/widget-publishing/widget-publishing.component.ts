import { ChangeDetector<PERSON>ef, Component, OnInit, Input } from '@angular/core';

// app services
import { AssetLibraryService, IAssetLibraryConfig} from '../services/asset-library.service';
import { AssignedUsersService } from '../assigned-users.service';
import { AuthScopeSettingsService, AuthScopeSetting } from "../auth-scope-settings.service";
import { AuthService } from '../../api/auth.service';
import { DataGuardService } from '../../core/data-guard.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { ItemComponentEditService } from '../item-component-edit.service';
import { ItemMakerService } from '../item-maker.service';
import { LangService } from '../../core/lang.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { PrintAltTextService } from '../print-alt-text.service';
import { PrintModeService } from '../print-mode.service';
import { RoutesService } from '../../api/routes.service';
import { ScriptGenService } from '../script-gen.service';
import { styleProfileOptions, StyleprofileService } from '../../core/styleprofile.service';
import { WhitelabelService } from '../../domain/whitelabel.service';

import { ItemSetPreviewCtrl } from '../item-set-editor/controllers/preview';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import { AssetLibraryCtrl } from '../item-set-editor/controllers/asset-library';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { ItemEditCtrl } from '../item-set-editor/controllers/item-edit';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { MemberAssignmentCtrl } from '../item-set-editor/controllers/member-assignment';
import { ItemFilterCtrl } from '../item-set-editor/controllers/item-filter';
import { PanelCtrl } from '../item-set-editor/controllers/mscat';
import { ItemSetPrintViewCtrl } from '../item-set-editor/controllers/print-view';
import { IReleaseHistory, ItemSetPublishingCtrl } from '../item-set-editor/controllers/publishing';
import { FrameworkQuadrantCtrl } from '../item-set-editor/controllers/quadrants';
import { TestFormGen } from '../item-set-editor/controllers/testform-gen';
import { TestletCtrl } from '../item-set-editor/controllers/testlets';
import { FormControl } from '@angular/forms';
import { mtz } from '../../core/util/moment';
import { saveAs } from 'file-saver';
import { deepCollect, deepFind } from '../services/util';
import { TestFormConstructionMethod } from '../item-set-editor/models/assessment-framework';
import { IItemContentDiff, IItemSignOff, itemContentDiff, ITestDesignSignOff, legacyItemContentDiff } from '../widget-audits/util/content-diff';

@Component({
  selector: 'widget-publishing',
  templateUrl: './widget-publishing.component.html',
  styleUrls: ['./widget-publishing.component.scss']
})
export class WidgetPublishingComponent implements OnInit {

  

  @Input() assetLibraryCtrl:AssetLibraryCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl
  @Input() auditCtrl:ItemBankAuditor
  @Input() itemBankCtrl:ItemBankCtrl
  @Input() itemEditCtrl:ItemEditCtrl
  @Input() itemFilterCtrl:ItemFilterCtrl
  @Input() memberAssignmentCtrl:MemberAssignmentCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() previewCtrl:ItemSetPreviewCtrl
  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() publishingCtrl: ItemSetPublishingCtrl
  @Input() quadrantCtrl: FrameworkQuadrantCtrl
  @Input() saveLoadCtrl:ItemBankSaveLoadCtrl
  @Input() testFormGen: TestFormGen
  @Input() testletCtrl: TestletCtrl
  savingChangesToTestDesign: boolean = false;

  constructor(
    private lang: LangService,
    private editingDisabled: EditingDisabledService,
    private auth: AuthService,
    private changeDetector: ChangeDetectorRef,
    private routes: RoutesService,
    private login: LoginGuardService,
  ) { }

  filterNum = new FormControl();
  activePublishedTestDesign?:number;
  activeTestDesignForms?:any[];
  testDesignsIsSaving: {[key:number]: boolean} = {};

  ngOnInit(): void {
    this.filterNum.valueChanges.subscribe(()=>{
      if (this.filterNum.value==undefined || this.filterNum.value==null) {
        this.filterNum.setValue('')
        return
      }
      let num = ''
      for (let i = 0;i<this.filterNum.value.toString().length;i++) {
        const c = this.filterNum.value.toString().charAt(i)
        if (c>='0' || c<='9') {
          num+=c
        }
      }
      if (Number(num)!=this.filterNum.value) this.filterNum.setValue(Number(num))
      this.reset()
    })
  }
  isReadOnly = () => this.editingDisabled.isReadOnly(true, true);

  getReleases(): IReleaseHistory[] {
    const allReleases = this.publishingCtrl.testDesignReleaseHistory
    const numVal = this.filterNum.value ? Number(this.filterNum.value) : undefined
    if (numVal) {
      const filteredReleases = []
      for (let num = 0;num<numVal && num<allReleases.length;num++) {
        filteredReleases.push(allReleases[num])
      }
      return filteredReleases
    }
    return allReleases
  }


  /**
   * 
   * @param testDesign the test design to check.
   * @returns true if the test design has been signed off.
   */
  isSignedOff(testDesign: IReleaseHistory) {
    return testDesign.is_approved;
  }

  /**
   * 
   * @param testDesign the test design to check.
   * @returns true if the test design has been compared to another test design.
   */
  hasComparedTestDesign(testDesign: IReleaseHistory) {
    return testDesign.compared_to_td_id !== null && testDesign.compared_to_td_id !== undefined
  }

  /**
   * 
   * @param testDesign the test design to check.
   * @returns true i the test design has started the sign off process.
   */
  isSignOffStarted(testDesign: IReleaseHistory) {
    return testDesign.tdso_id !== null && testDesign.tdso_id !== undefined;
  }

  /**
   * 
   * @returns true if there are no releases that have been signed off.
   */
  hasNoSignOffs() {
    const releaseHistory = this.getReleases();

    const recentSignOff = releaseHistory.find((release) => 
      release.tdso_id != undefined
    )
    return recentSignOff === undefined;
  }

  isSignOffLoading = false;

  /**
   * Function for starting the sign off process. 
   * If there are no signed off test designs, the first test design will be signed off instantly.
   * Otherwise, the test design will be compared to the latest signed off test design. 
   * Then, the sign off records will be created and the user will be directed to the Sign Off mode.
   * @param testDesign the test design to sign off on
   */
  async startTestDesignSignOff(testDesign: IReleaseHistory) {
    this.isSignOffLoading = true;
    // Instant sign off if no current sign offs
    if(this.hasNoSignOffs()) {
      return this.auth.apiCreate(
        this.routes.TEST_AUTH_TEST_DESIGN_SIGN_OFF, 
        {
          test_design_id: testDesign.id, 
          is_approved: 1,
        }, 
      ).finally(() => {
        this.publishingCtrl.loadTestDesignReleaseHistory();
        this.isSignOffLoading = false;
      });
    }

    // Create test design sign off info
    const releaseHistory = this.getReleases();
    
    const approvedTestDesign = releaseHistory.find((release) => 
      release.is_approved == 1
    )

    if(!approvedTestDesign) {
      return this.login.quickPopup('No valid test designs found.');
    }

    const testDesignIds = [testDesign.id, approvedTestDesign.id];

    const data: any[] = await this.auth.apiFind(this.routes.TEST_DESIGN_QUESTION_VERSIONS, {query: {test_design_ids: testDesignIds}})
    const parsedData = itemContentDiff(data, false, {itemBankCtrl: this.itemBankCtrl}, `${testDesign.id}`);
    const diffDataFiltered = parsedData.filter((data) => data.hasDiff == true);
    const qIds = diffDataFiltered.map((item) => +item.questionId);

    // If no diffs found, instant sign off
    if(diffDataFiltered.length == 0) {
      return this.auth.apiCreate(
        this.routes.TEST_AUTH_TEST_DESIGN_SIGN_OFF, 
        {
          test_design_id: testDesign.id, 
          compared_to_td_id: approvedTestDesign.id,
          is_approved: 1,
        }, 
      ).finally(() => {
        this.publishingCtrl.loadTestDesignReleaseHistory();
        this.isSignOffLoading = false;
      });
    }
    // Initialize data map

    try {
      const signOffRecords = await this.itemBankCtrl.createSignOffRecords(testDesign.id, approvedTestDesign.id, diffDataFiltered);
      this.publishingCtrl.loadTestDesignReleaseHistory();
      
      const dataMap = this.itemBankCtrl.initDiffDataMap(diffDataFiltered);
  
      const itemSignOffRecordMap = this.itemBankCtrl.inititemSignOffRecordMap(signOffRecords.itemSignOffRecords);
  
      // Go to authoring view with items in filter
      this.itemBankCtrl.activateSignOffView(qIds, dataMap, testDesignIds, signOffRecords.signOffRecord, itemSignOffRecordMap);
    } catch (err) {
      this.login.quickPopup(`Error initializing sign off: ${err.message}`);
      this.isSignOffLoading = false;
      throw new Error(err)
    }
    this.isSignOffLoading = false;
  }

  /**
   * Function for restoring/continuing the Sign Off process for a test design that is in progress.
   * @param tdsoId the existing test_design_sign_off ID to restore.
   */
  async restoreTestDesignSignOff(tdsoId: number) {
    try {
      const signOffRecords: {signOffRecord: ITestDesignSignOff, itemSignOffRecords: IItemSignOff[]} = await this.auth.apiGet(this.routes.TEST_AUTH_TEST_DESIGN_SIGN_OFF, tdsoId);

      if(!signOffRecords) {
        throw new Error('MISSING_SIGN_OFF')
      }

      const diffData: IItemContentDiff[] = JSON.parse(signOffRecords.signOffRecord.audit_config);
      const qIds = diffData.map((item) => +item.questionId);

      const dataMap = this.itemBankCtrl.initDiffDataMap(diffData);
      const itemSignOffRecordMap = this.itemBankCtrl.inititemSignOffRecordMap(signOffRecords.itemSignOffRecords);
  
      const testDesignIds = [signOffRecords.signOffRecord.test_design_id, signOffRecords.signOffRecord.compared_to_td_id]

      // Go to authoring view with items in filter
      this.itemBankCtrl.activateSignOffView(qIds, dataMap, testDesignIds, signOffRecords.signOffRecord, itemSignOffRecordMap);
    } catch (err) {
      this.login.quickPopup(`Error loading sign off: ${err.message}`);
      throw new Error(err)
    }
  }


  async selectPublishedTestDesign(testDesignId: number){
    this.activePublishedTestDesign = testDesignId;
    this.activeTestDesignForms = await this.publishingCtrl.loadFormsForPublishedTestDesign(testDesignId);
  }

  clearActiveTestDesignForms(){
    this.activeTestDesignForms = null;
    this.activePublishedTestDesign = null;
  }

  async revokeForm(test_form:{id:number, is_revoked:number}){
    await this.publishingCtrl.revokeForm(test_form.id);
    test_form.is_revoked = 1;
  }
  async unrevokeForm(test_form:{id:number, is_revoked:number}){
    await this.publishingCtrl.unrevokeForm(test_form.id);
    test_form.is_revoked = 0;
  }

  renderTwTitle(twTitle:any){
    if (twTitle){
      try {
        const title = JSON.parse(twTitle);
        return title[this.lang.c()] || title['en']
      }
      catch(e){
        return '---'
      }
    }
  }

  renderDate(dateStr, formatStrSlug:string = "datefmt_timestamp"){
    if (!dateStr) return null;
    return mtz(dateStr).format(this.lang.tra(formatStrSlug))
  }

  resetter = true;
  reset() {
    this.resetter = false;
    this.changeDetector.detectChanges()
    this.resetter = true;
  }

  formTableColumnTitles = {
    id: "ID",
    source_tf_id: "Form/Panel Name",
    lang: "Lang",
    num_students_assigned: "Students Assigned",
    num_students_submitted: "Students Submitted",
    created_on: "Created On",
    last_assigned_on: "Last Assigned On",
    is_revoked: "Revoked?"
  }

  formTableColumnTitlesArr = Object.values(this.formTableColumnTitles);


  formTableDownloadColumnsTLOFT = {
    testlet:"Testlet IDs",
    questions: "Item IDs",
  }

  async downloadFormsTable() {
    if (!this.activeTestDesignForms) return;
    const publishedForms = await this.publishingCtrl.getPublishedTestFormsByUrl(this.activePublishedTestDesign, this.activeTestDesignForms.length, 300);
    let blob;
    switch(this.frameworkCtrl.asmtFmrk.testFormType){
      case TestFormConstructionMethod.TLOFT:
        blob = this.generateDownloadBlobForTLOFT(publishedForms);
        break;
      case TestFormConstructionMethod.LINEAR:
      case TestFormConstructionMethod.MSCAT:
        const header = Object.keys(this.activeTestDesignForms[0]).map(key => this.formTableColumnTitles[key]).join(',');
        const rows = this.activeTestDesignForms.map(obj => Object.values(obj).join(','));
        const csvData = `${header}\n${rows.join('\n')}`;
        blob = new Blob([csvData], { type: 'text/csv' });   
        break;
      }
      const currentDate = mtz().format('YYYY-MM-DD HH:mm');
      saveAs(blob, `test_design_${this.activePublishedTestDesign}_test_forms_${currentDate}`);
  }

  async downloadTFMITable(){
    const formsTFMI = await this.publishingCtrl.getPublishedTestFormsTFMI(this.activePublishedTestDesign);
    const header = Object.keys(formsTFMI[0]);
    const rows = formsTFMI.map(obj => Object.values(obj).join(','));
    const csvData = `${header}\n${rows.join('\n')}`;
    const blob = new Blob([csvData], { type: 'text/csv' });   
    const currentDate = mtz().format('YYYY-MM-DD HH:mm');
    saveAs(blob, `test_design_${this.activePublishedTestDesign}_test_form_module_items_${currentDate}`);
  }
  
  generateDownloadBlobForTLOFT(publishedForms:any){
    const header = [...this.formTableColumnTitlesArr];
    header.splice(2, 0, ...Object.values(this.formTableDownloadColumnsTLOFT)).join(',');
    const rows = this.activeTestDesignForms.map(obj =>{
      const publishedForm = publishedForms[obj.id]
      const testlets = '"' + publishedForm.testletIds.join(', ') + '"';
      const questions = deepCollect(publishedForm, 'questions', true)
        .concat(deepCollect(publishedForm, 'preambleList'))
        .concat(deepCollect(publishedForm, 'postambleList'));
      const uniqueQuestions = new Set();
      questions.forEach(questionArr =>{
        questionArr.forEach(question =>{
          uniqueQuestions.add(question)
        });
      });
      const formattedQuestions = '"'+Array.from(uniqueQuestions).join(', ') + '"';
      const row = Object.values(obj);
      row.splice(2,0, [testlets, formattedQuestions]);
      return row.join(',')
    });
    const csvData = `${header}\n${rows.join('\n')}`;
    const blob = new Blob([csvData], { type: 'text/csv' });   
    const currentDate = mtz().format('YYYY-MM-DD HH:mm')
    saveAs(blob, `test_design_${this.activePublishedTestDesign}_test_forms_${currentDate}`);
  }

  async saveTestDesign(id:number){
    this.testDesignsIsSaving[id] = true;
    try{
      await this.publishingCtrl.updateTestDesigns(id);
    } catch(e){
      console.error(e);
    }
    this.testDesignsIsSaving[id] = false;

  }
}