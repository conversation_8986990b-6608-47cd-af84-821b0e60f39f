@import '../../widget-authoring-main/widget-authoring-main.component.scss';
@import '../../../../styles/partials/_modal.scss';
//CSS for the split screen modal
 

.divider-container {
  display: flex;
  justify-content: space-between; /* Align dividers at the ends */
}

.divider {
  flex: 1; /* Each divider takes equal space */
}

.input-container {
  display: flex;
  align-items: center; /* Align items vertically */
}

.label {
  margin-right: 10px; /* Adjust spacing between label and input */
}

.question-runner-container {
  height: 95%;
  overflow: auto;
  display: flex;
}

.question-runner-left,
.question-runner-right {
  flex: 1; /* Allow containers to grow as needed */
  padding: 20px;
  box-sizing: border-box;
  border: 2px solid #ccc; /* Add border to the question runners */
  overflow: auto; /* Enable scrolling */
  padding-top: 3%;
  padding-bottom: 3%;
}
.question-runner-left {
  border-right: none; /* Remove right border from left question runner */
}
.question-runner {
  width: 100%;
  padding: 20px;
}
.question-container {
  margin-bottom: 10px;
  height: 45vh;
  overflow-y: scroll;
}

.diff-container-sugg{
  background-color: #d9ecf8;
}

.diff-container-real{
  background-color: #d5ffee;
}
.flex-container {
  display: flex;
  align-items: center;
}
//top and bottom style the diff section title and the check all diffs
.flex-grow {
  flex-grow: 1;
}
//for the label of the view all changes checkbox
.checkbox-label {
  display: flex;
  align-items: center;
}
input[type="checkbox"] {
  vertical-align: middle;
}

.diff-renderer-container{
  border: 1px solid rgba(0, 0, 0, 0.1); 
  padding: 1em;
  border-radius: 5px;
}
