<div class="is-flex" style="justify-content: space-between; flex-direction: column;" >
    
  <div class="is-fullwidth is-flex" style="justify-content: space-between;">
    <div>
      <h1 *ngIf="isContentSugg()" class="title has-text-info">Suggestion</h1>
      <h1 *ngIf="isContentReal()" class="title has-text-primary">Real Question</h1>
    </div>
    <div>
      <button 
        class="button is-small has-icon"
        [class.is-info]="isViewBefore() && isContentSugg()"
        [class.is-primary]="isViewBefore() && isContentReal()"
        (click)="setViewBefore()"
      >Before Change</button>
      <button 
        class="button is-small has-icon" 
        [class.is-info]="isViewAfter() && isContentSugg()"
        [class.is-primary]="isViewAfter() && isContentReal()"
        (click)="setViewAfter()"
      >After Change</button>
      <button 
        class="button is-small has-icon" 
        [class.is-info]="isViewSplit() && isContentSugg()"
        [class.is-primary]="isViewSplit() && isContentReal()"
        (click)="setViewSplit()"
      >Split View</button>
    </div>
  </div>
    <div *ngIf="!isViewSplit()" class = "question-container" [class.is-hi-contrast]="isHighContrast">
      <!-- Repeated to avoid bugs in rendering when config input changes -->
      <question-runner 
      *ngIf="isViewBefore()"
      [currentQuestion]="getQuestionBefore()" 
      [questionState]="{}"
      [isSubmitted]="isSubmitted"
      [isPrintMode]="isResultsPrint"
      [selectedEditEntry]="selectedEntry"
      ></question-runner>
      <question-runner 
      *ngIf="isViewAfter()"
      [currentQuestion]="getQuestionAfter()" 
      [questionState]="{}"
      [isSubmitted]="isSubmitted"
      [isPrintMode]="isResultsPrint"
      [selectedEditEntry]="selectedEntry"
      ></question-runner>
    </div>
      <!-- For Split Screen View -->
    <div *ngIf="isViewSplit()" class="question-container">
        <div class="divider-container">
          <div class="divider">
            <div class="label">Before</div>
            <div class="input-container">
              <input type="range" min="10" max="200" [(ngModel)]="splitScreenZoom.left">
              <i class="fas fa-search"></i>
            </div>
            
          </div>
          <div class="divider">
            <div class="label">After</div>
            <div class="input-container">
              <input type="range" min = '10' max = '200' [(ngModel)]="splitScreenZoom.right">
              <i class="fas fa-search"></i>
            </div>
            
          </div>
        </div>
        <div class="question-runner-container">
          <div class="question-runner-left">
            <span  [style.zoom]="getZoomFactor(splitScreenZoom.left)" >   
              <question-runner 
                [currentQuestion]="getQuestionBefore()" 
                [questionState]="{}"
                [isSubmitted]="isSubmitted"
                [isPrintMode]="isResultsPrint"
                [selectedEditEntry]="selectedEntry"
              ></question-runner>
            </span>
          </div>
          <div class="question-runner-right"  >
            <span [style.zoom]="getZoomFactor(splitScreenZoom.right)">
              <question-runner 
                [currentQuestion]="getQuestionAfter()" 
                [questionState]="{}"
                [isSubmitted]="isSubmitted"
                [isPrintMode]="isResultsPrint"
                [selectedEditEntry]="selectedEntry"
              ></question-runner>
            </span>
          </div>
        </div>
    </div>

    

  <div>
      <button class="button is-small is-info  has-icon" (click)="toggleSubmitted()" [class.is-outlined]="!isSubmitted">
        <span class="icon"><i class="fa" [class.fa-lock]="!isSubmitted" [class.fa-unlock]="isSubmitted"></i></span>
        <span>{{!isSubmitted ? lang.tra('auth_simulate_submission') : lang.tra('auth_undo_submission')}}</span>
      </button>
      <button class="button is-small is-info has-icon" [class.is-outlined]="!isResultsPrint" (click)="toggleResultsPrint()">
        <span class="icon"><i class="fa" [class.fa-print]="!isResultsPrint" [class.fa-arrow-left]="isResultsPrint"></i></span>
        <span>{{!isResultsPrint ? lang.tra('auth_results_print_mode') : lang.tra('auth_leave_results_print_mode')}}</span>
      </button>
      <button class="button is-small is-info has-icon" [class.is-outlined]="!isHighContrast" (click)="toggleHighContrast()">
        <span><tra slug="btn_hi_contrast"></tra></span>
      </button>
  </div>

  <div 
    class="box is-rounded"
    [class.diff-container-sugg]="isContentSugg()"
    [class.diff-container-real]="isContentReal()"
  >
    <div class="flex-container">
      <h2>Diffs</h2>
      <span class="flex-grow"></span>
      <label *ngIf="hiddenRenderedDiffs.length" class="checkbox-label">
        <input type="checkbox"  [checked]="viewHiddenRenderedDiffs" (change)="toggleHiddenDiffs()">
        <tra slug="ie_view_all_diffs"></tra>
      </label>
    </div>
    <div *ngIf="renderedDiffs?.length" class="diff-renderer-container">
      <h3><tra slug="ie_content_change_diffs"></tra></h3>
      <div *ngFor="let diff of renderedDiffs; let i = index">
        <expansion-panel
          [title]= "diff.title"
          [subTitle] = "diff.subTitle"
          [description] = "diff.description"
          [content] = "diff.content"
          [style] = "diff.style"
          [signalOpenClose] = "true"
          [isExpanded] = "diff.isExpanded"
          [readOnly] = "true"
          (opened) = "diffExpansionOpened(diff, i)"
          (closed) = "diffExpansionClosed(diff)"
        ></expansion-panel>
      </div>
    </div>
    <div *ngIf="viewHiddenRenderedDiffs" class="diff-renderer-container">
      <h3><tra slug="ie_setting_change_diffs"></tra></h3>
      <div *ngFor="let diff of hiddenRenderedDiffs; let i = index">
        <expansion-panel
        [title]= "diff.title"
        [subTitle] = "diff.subTitle"
        [description] = "diff.description"
        [content] = "diff.content"
        [style] = "diff.style"
        [signalOpenClose] = "true"
        [isExpanded] = "diff.isExpanded"
        [readOnly] = "true"
        (opened) = "diffExpansionOpened(diff, i, 'hidden')"
        (closed) = "diffExpansionClosed(diff)"
        ></expansion-panel>
      </div>
    </div>
  </div>   
</div>