import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { EditType, ItemComponentEditService } from '../../item-component-edit.service';
import { LangService } from 'src/app/core/lang.service';
import { ItemBankCtrl } from '../../item-set-editor/controllers/item-bank';
import { ContentType, ILogEntryData, IRenderedDiff, TextDiffColors } from './../model'
import { EditViewMode } from '../../item-set-editor/models/types';
import { CustomSpanData, ExpansionPanelContentType } from './../../../ui-partial/expansion-panel/expansion-panel.component'
import { isDiffInLogRemoved, isDiffInLogHidden } from './diffsStateOptions';
import * as Diff from 'diff';
import * as _ from 'lodash';



@Component({
  selector: 'display-config-diff',
  templateUrl: './display-config-diff.component.html',
  styleUrls: ['./display-config-diff.component.scss']
})
export class DisplayConfigDiffComponent implements OnInit {

  constructor(
    public itemComponentEdit: ItemComponentEditService,
    public lang: LangService,
    public itemBankCtrl: ItemBankCtrl
  ) { }
  @Input() content;
  @Output() isSplitViewEvent = new EventEmitter<String>();
  timelineContent: ILogEntryData[] = [];
  currEditViewMode: EditViewMode;
  renderedDiffs: IRenderedDiff[] = [];
  hiddenRenderedDiffs: IRenderedDiff[] = [];
  isSubmitted = false;
  isResultsPrint = false;
  isHighContrast = false;
  selectedEntry;
  viewHiddenRenderedDiffs = false;//to control whether hidden diffs are showing
  splitScreenZoom = {left: 100, right:100}
  ngOnInit(): void {
    this.setViewAfter();
    this.refreshDiffRender();
    this.outlineConfigBlock();
  }
  ngOnChanges() {
    this.setViewAfter();
    this.refreshDiffRender();
    this.outlineConfigBlock();
  }

  isContentSugg() {
    return this.content.contentType == ContentType.SUGGESTION;
  }

  isContentReal() {
    return this.content.contentType == ContentType.REAL;
  }

  refreshDiffRender() {
    this.viewHiddenRenderedDiffs = false;
    this.renderedDiffs = [];
    this.hiddenRenderedDiffs = []
    const rawDiffs = this.itemComponentEdit.deepDiff(this.content.details.configBefore, this.content.details.configAfter, false, true);
    rawDiffs.forEach(rawDiff => {
      const renderedDiff = this.itemComponentEdit.getRenderedDiff(rawDiff)
      //Basically if it's in hidden diffs it'll show up here otherwise it'll show up normally
      if (renderedDiff && isDiffInLogHidden(rawDiff, this.content.details.configBefore)) {
        this.hiddenRenderedDiffs.push(renderedDiff)
      }
      else if(renderedDiff && !isDiffInLogRemoved(rawDiff)){
        this.renderedDiffs.push(renderedDiff)
      }
    });
  }

  isViewableFieldDiff(d) {
    return this.itemComponentEdit.isViewableField(_.last(d.path), _.get(this.getQuestionContent(), _.dropRight(d.path)));
  }

  getQuestionContent() {
    const questionConfig = this.isViewBefore() ? this.content.details.configBefore : this.content.details.configAfter;
    let stateArr = Object.keys(this.content.details.configBefore)
    if (this.lang.getCurrentLanguage() === 'fr' && stateArr.includes('logType') && this.content.details.configBefore.logType === 'SUGGESTION_EDIT') {
      return questionConfig
    }
    return this.itemBankCtrl.getQuestionContent(questionConfig)
  }
  getQuestionBefore(){
    let stateArr = Object.keys(this.content.details.configBefore)
    if (this.lang.getCurrentLanguage() === 'fr' && stateArr.includes('logType') && this.content.details.configBefore.logType === 'SUGGESTION_EDIT') {
      return this.content.details.configBefore
    }
    return this.itemBankCtrl.getQuestionContent(this.content.details.configBefore)
  }
  getQuestionAfter(){
    let stateArr = Object.keys(this.content.details.configBefore)
    if (this.lang.getCurrentLanguage() === 'fr' && stateArr.includes('logType') && this.content.details.configBefore.logType === 'SUGGESTION_EDIT') {
      return this.content.details.configAfter
    }
    return this.itemBankCtrl.getQuestionContent(this.content.details.configAfter)
  }
  getQuestionConfig() {
    const questionConfig = this.isViewBefore() ? this.content.details.configBefore : this.content.details.configAfter;
    return questionConfig
  }


  toggleSubmitted() {
    this.isSubmitted = !this.isSubmitted;
  }

  toggleResultsPrint() {
    this.isResultsPrint = !this.isResultsPrint;
  }

  toggleHighContrast() {
    this.isHighContrast = !this.isHighContrast;
  }

  /**
 * Sets the input into question-runner which will cause a sub-element to be outlined within the modal. If called with no parameters will remove the outline.
 *
 * @param entryId - The entryId of the sub-element in the config to outline
 * @param editType - The edit type which determines the outline colour
 */
  outlineConfigBlock(entryId?: number, editType?: EditType) {
    this.selectedEntry = {
      id: entryId,
      border: this.itemComponentEdit.getBorder(editType)
    }
  }

  diffExpansionOpened(diff, i, type = 'visible') {
    // Ensure that it's closing all other panels the hidden visible type is to ensure it's not opening any of the hidden with the same index
    if (type === 'visible') {
      this.renderedDiffs.forEach((data, index) => {
        data.isExpanded = index === i;
      });
      this.hiddenRenderedDiffs.forEach((data) => {
        data.isExpanded = false;
      });
    }
    if (type === 'hidden') {
      this.hiddenRenderedDiffs.forEach((data, index) => {
        data.isExpanded = index === i;
      });
      this.renderedDiffs.forEach((data) => {
        data.isExpanded = false;
      });
    }
    // Find which sub-element entryId was affected and call to outline this block in the config
    let entryId;
    switch (diff.editType) {
      case EditType.EDITED:
        const element = _.get(this.getQuestionContent(), _.dropRight(diff?.originalDiff?.path))
        entryId = element?.entryId
        break;
      case EditType.ADDED:
        entryId = diff?.originalDiff?.item?.rhs?.entryId;
        break;
      case EditType.DELETED:
        entryId = diff?.originalDiff?.item?.lhs?.entryId;
        break;
    }
    this.outlineConfigBlock(entryId, diff.editType)
  }

  diffExpansionClosed(diff) {
    diff.isExpanded = false;
    // If closing an expanded panel which was already opened (only one can be opened), call to remove the outline
    // Otherwise it's an automatic closing because another panel was opened, in which case outline is changed upon opening
    if (!this.renderedDiffs.some(diff => {
      return diff.isExpanded
    })) this.outlineConfigBlock();
  }


  setViewBefore() {
    this.isSplitViewEvent.emit(EditViewMode.BEFORE);
    this.currEditViewMode = EditViewMode.BEFORE;
  }
  setViewAfter() {
    this.isSplitViewEvent.emit(EditViewMode.AFTER);
    this.currEditViewMode = EditViewMode.AFTER;
  }
  setViewSplit() {
    this.isSplitViewEvent.emit(EditViewMode.SPLIT);
    this.currEditViewMode = EditViewMode.SPLIT;
  }
  isViewBefore(): Boolean {
    return this.currEditViewMode == EditViewMode.BEFORE;
  }
  isViewAfter(): Boolean {
    return this.currEditViewMode == EditViewMode.AFTER;
  }
  isViewSplit(): Boolean {
    return this.currEditViewMode == EditViewMode.SPLIT;
  }
  toggleHiddenDiffs() {
    this.viewHiddenRenderedDiffs = !this.viewHiddenRenderedDiffs
  }

  getZoomFactor(zoom:number):string{
    return `${zoom}%`
  }

}