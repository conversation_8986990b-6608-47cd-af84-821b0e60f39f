import { Component, OnInit, Input } from '@angular/core';
import { ItemComponentEditService } from '../item-component-edit.service';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';
import { EditViewMode } from '../item-set-editor/models/types';
import { ColDef, GridOptions, IGetRowsParams} from 'ag-grid-community';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import {ContentType, ILogEntryData, ChangeLogType, ActionType, IFilterSettings, LogFilters  } from './model'
import { mtz } from '../../core/util/moment';
import { Observable, of } from 'rxjs';
import { requestStatusOptions } from '../graphics-workflow-section/graphics-request/graphics-request.component'
import { FormBuilder, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';

@Component({
  selector: 'widget-change-log-modal',
  templateUrl: './widget-change-log-modal.component.html',
  styleUrls: ['./widget-change-log-modal.component.scss']
})
export class WidgetChangeLogModalComponent implements OnInit {

  @Input() currQuestionId: number;
  @Input() currQuestionSuggestionId: number;
  @Input() questionLabel: string;

  EditViewMode = EditViewMode;
  ContentType = ContentType;
  logEntryDataByPage:{[key: string] : ILogEntryData[]} = {};
  currEditViewMode = EditViewMode.AFTER;
  currContent;
  selectedEntry;
  filteredByDate = false;
  filterDate = {start:undefined, end:undefined};
  versionComparison = {v1: undefined, v2: undefined};
  private gridApi;
  private gridColumnApi;
  logFilterStartDate;
  formGroup: FormGroup;
  selectedRowIndex: any;
  rowIndexOnPage: number;
  isComparingVersions: boolean = false;
  constructor(
    private fb: FormBuilder,
    private auth: AuthService,
    private routes: RoutesService,
    public itemComponentEdit: ItemComponentEditService,
    public lang: LangService,
    public itemBankCtrl: ItemBankCtrl
  ) { }

  // By default don't allow sort and filtering on ag-grid - due to pagination it would need to be implemented in API first 
  defaultColDef: ColDef = {
    sortable: false,
    resizable: true,
  };
  
  changeLogColDefs: ColDef[] = [
    { headerName: 'Time', field: 'time', width: 230}, 
    { headerName: 'User', field: 'author', width: 400 },
    { headerName: 'Action', field: 'contentAction', width: 300 },
    { headerName: 'Type', field: 'contentType', width: 200 },

  ];
  changeLogGridOptions: GridOptions = {
    rowSelection: 'single',
    columnDefs: this.changeLogColDefs,
    defaultColDef: this.defaultColDef,
    cacheBlockSize: 12,
    paginationPageSize: 12,
    rowModelType: 'infinite',
  };
  
  ngOnInit(): void {
    //this sets up the form group for date filtering
    this.formGroup = this.fb.group({
      logFilterStartDate: [null], // Including Validators.required
      logFilterEndDate: [null], // Including Validators.required
    }, { validator: this.dateRangeValidator() }); // Apply the date range validator to the FormGroup
  }

  onFilterChange(){
    // Manually set the grid pagination to the first page
    this.gridApi.paginationGoToFirstPage();
    // Trigger a refresh of the grid data
    this.gridApi.purgeInfiniteCache();
  }
  filterByDate(){
    this.onFilterChange();
    this.filteredByDate = true;
  }
  clearDateFilters(){
    if(this.filteredByDate) this.onFilterChange();
    this.formGroup.controls.logFilterStartDate.setValue(null);
    this.formGroup.controls.logFilterEndDate.setValue(null);
    this.filteredByDate = false;
  }
  // Possible filters for the log, by default real/suggested checked on - gets all log types
  logQueryFilterSettings: IFilterSettings[] = [
    {slug: ContentType.REAL, filter: LogFilters.filter_real, checked: true},
    {slug: ContentType.SUGGESTION, filter: LogFilters.filter_suggestion, checked: true},
    {slug: ContentType.TRACKING_CHANGES, filter: LogFilters.filter_tracking_changes, checked: false},
    {slug: ContentType.WORKFLOW_STAGE, filter: LogFilters.filter_workflow_stage, checked: false},
    {slug: ContentType.GRAPHIC_REQUEST, filter: LogFilters.filter_graphic_request, checked: false}
  ]

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    // Set up data source for API-side pagination
    const datasource = {
      getRows: (params: IGetRowsParams) => {
        // Pass the offset and limit based on the first row of the new page, and the page size
        this.loadLogEntries(params.startRow, this.changeLogGridOptions.paginationPageSize)
          .subscribe(data => { 
            params.successCallback(data.logEntryData, data.totalCount) 
          });
      }
    }
    this.gridApi.setDatasource(datasource);
  }

  onPaginationChanged($event){
    this.resetSelected()
  }

  resetSelected() {
    this.currEditViewMode = EditViewMode.AFTER;
    this.currContent = null;
  }


  /** Find the full data for the selected row and store it in `currContent` */
  onRowSelected(event: any) {
    if (!event.node.isSelected()) return;
    const selectedRowIndex = event.rowIndex;
    const rowIndexOnPage = selectedRowIndex % this.changeLogGridOptions.paginationPageSize
    const currentPageIndex = this.gridApi.paginationGetCurrentPage();
    //We're checking to see if we're in comparison mode
    if(this.isComparingVersions){
      if(this.versionComparison.v1){//checking checking which version the row selected should take v1 or v2 if v1 is already taken then assign v2  otherwise assign v1 
        this.versionComparison.v2 = this.logEntryDataByPage[currentPageIndex][rowIndexOnPage].details.configAfter;
        this.versionComparison.v2['time'] = this.logEntryDataByPage[currentPageIndex][rowIndexOnPage].time;//this is being done so we have a time so users can identify it with the table entry somewhat
      } else {
        this.versionComparison.v1 = this.logEntryDataByPage[currentPageIndex][rowIndexOnPage].details.configAfter;
        this.versionComparison.v1['time'] = this.logEntryDataByPage[currentPageIndex][rowIndexOnPage].time;

      }
    } else {
      this.currContent = this.logEntryDataByPage[currentPageIndex][rowIndexOnPage];
    }
  }
  /**
   * Fetch and process data for the current page in the table
   * @param offset - offset when fetching records
   * @param limit  - limit when fetching records
   * @returns An Observable with the log content for the current page, and total number of records
   */
  loadLogEntries(offset:number, limit:number): Observable<{ logEntryData: any, totalCount: number }> {
    // Pass which filters were checked off into the params to only return specified logs
    const filterParams = {}
    this.logQueryFilterSettings.forEach(setting => {
      filterParams[setting.filter] = setting.checked ? 1 : 0
    })
    return new Observable(observer => {
      this.auth.apiFind(this.routes.TEST_AUTH_QUESTION_CHANGE_LOG, {
        query: {
          dateRange: this.filterDate.start !== undefined || this.filterDate.end !== undefined? this.filterDate: undefined,//This is so that filter date only is passed if start or end is defined
          offset,
          limit,
          test_question_id: this.currQuestionId,
          lang: this.lang.c(),
          ...filterParams
        }
        
      }).then(res => {
        const {log, fullInfo, totalCount} = res
        const logEntryData = this.processLogEntryData(log, fullInfo)
        const currentPageIndex = this.gridApi.paginationGetCurrentPage();
        // Loading will only be called if this page was not visited before. Therefore preserve all full data, group by pages
        this.logEntryDataByPage[currentPageIndex] = logEntryData
         // Emit the processed data to subscribers
        observer.next({logEntryData, totalCount});
        observer.complete();
      }).catch(error => {
        observer.error(error); 
      });
    })
  }

  /** Given log records and info returned from API, process and reorganize data for the ag-grid table and detailed view */
  processLogEntryData(log:[], fullInfo){
    const {userInfo, suggVersionInfo, questionVersionInfo, stageInfo} = fullInfo;
    const uidToDetail = (uid) => `${userInfo[uid]?.first_name} ${userInfo[uid]?.last_name} (${userInfo[uid]?.contact_email})`
    return log.map((rawLog:any) => {
      let {log_type, created_by_uid, created_on} = rawLog;
      const log_data = JSON.parse(rawLog?.log_data || "{}");
      const details:any = {}
      let isExpandView = false;
      if (log_type == ChangeLogType.REAL_EDIT) {
        isExpandView = true;
        details.configBefore = JSON.parse(questionVersionInfo[log_data.prev_test_question_version_id]?.config || "{}")
        details.configAfter = JSON.parse(questionVersionInfo[log_data.test_question_version_id]?.config || "{}")
      }
      else if (log_type == ChangeLogType.SUGGESTION_EDIT) {
        isExpandView = true;
        details.configBefore = JSON.parse(suggVersionInfo[log_data.prev_suggestion_version_id]?.config || "{}")
        details.configBefore.logType = 'SUGGESTION_EDIT'
        details.configAfter = JSON.parse(suggVersionInfo[log_data.suggestion_version_id]?.config || "{}")
        details.configAfter.logType = 'SUGGESTION_EDIT'

      }
      else if (log_type == ChangeLogType.STAGE_ASSIGNMENT) {
        isExpandView = true;
        const {assigned_uids, prev_assigned_uids} = log_data;

        details.assignedBefore = prev_assigned_uids.map(uidToDetail)
        details.assignedAfter = assigned_uids.map(uidToDetail)
      }

      if (log_type == ChangeLogType.GRAPHIC_REQUEST_STATUS && log_data.assigned_uid) {
        log_data.assignedUser = uidToDetail(log_data.assigned_uid)
      }

      if ([ChangeLogType.STAGE_ASSIGNMENT, ChangeLogType.STAGE_TRANSITION, ChangeLogType.STAGE_STATUS].includes(log_type)) {
        log_data.stage_order = stageInfo[log_data?.stage_id]?.stage_order
        log_data.stage_name = this.lang.tra(this.itemBankCtrl.getStageByOrder(log_data.stage_order)?.slug)
        if (log_type == ChangeLogType.STAGE_ASSIGNMENT) details.stageTitle = `Stage ${log_data.stage_order} - ${log_data.stage_name}`
      }
      const {contentType, contentAction} = this.getTypeAndAction(log_type, log_data)

      return {
        isExpandView,
        details,
        contentAction,
        contentType,
        author: uidToDetail(created_by_uid),
        time: this.processDate(created_on)
      }
    })
  }

  isConfigDiff(content:ILogEntryData){
    return content.contentType == ContentType.REAL || content.contentType == ContentType.SUGGESTION
  }
  isAssigneeDiff(content:ILogEntryData){
    return content.contentType == ContentType.WORKFLOW_STAGE && content.isExpandView
  }

  /** Based on the log info, return the type and action strings that will define it in the ag-grid table  */
  getTypeAndAction (logType: ChangeLogType, cleanLogData) {

    const getGraphicReqModifier = (cleanLogData) => {
      const {graphic_request_id, is_revision} = cleanLogData;
      return ` (ID: ${graphic_request_id}${is_revision ? ', revision' : ''})`
    }

    let contentType;
    let contentAction;

    if (logType == ChangeLogType.ITEM_CREATION) {
      contentType = ContentType.REAL,
      contentAction = ActionType.ITEM_CREATION
    }
    if (logType == ChangeLogType.TRACKING_CHANGES_ENTER) {
      contentType = ContentType.TRACKING_CHANGES,
      contentAction = ActionType.TRACKING_CHANGES_ENTER
    }
    else if (logType == ChangeLogType.TRACKING_CHANGES_LEAVE) {
      contentType = ContentType.TRACKING_CHANGES,
      contentAction = ActionType.TRACKING_CHANGES_LEAVE
    }
    else if (logType == ChangeLogType.REAL_EDIT) {
      contentType = ContentType.REAL;
      const {is_accept_sugg} = cleanLogData;
      contentAction = is_accept_sugg ? ActionType.REAL_EDIT_ACCEPT_SUGG : ActionType.REAL_EDIT_DIRECT
    }
    else if (logType == ChangeLogType.SUGGESTION_EDIT) {
      contentType = ContentType.SUGGESTION;
      const {is_reject_sugg} = cleanLogData;
      contentAction = is_reject_sugg ? ActionType.SUGGESTION_EDIT_REJECT_SUGG : ActionType.SUGGESTION_EDIT_DIRECT
    }
    else if (logType == ChangeLogType.STAGE_STATUS) {
      contentType = ContentType.WORKFLOW_STAGE;
      if (cleanLogData.is_signed_off) contentAction = ActionType.SIGNED_OFF
      else if (cleanLogData.is_remove_signed_off) contentAction = ActionType.SIGNED_OFF_REMOVE
      else if (cleanLogData.is_done_editing) contentAction = ActionType.DONE_EDITING
      else if (cleanLogData.is_remove_done_editing) contentAction = ActionType.DONE_EDITING_REMOVE
      else if (cleanLogData.is_edit_review) contentAction = ActionType.EDIT_REVIEW
      else if (cleanLogData.is_remove_edit_review) contentAction = ActionType.EDIT_REVIEW_REMOVE
      else if (cleanLogData.is_changes_required) contentAction = ActionType.CHANGES_REQUIRED
      else if (cleanLogData.is_remove_changes_required) contentAction = ActionType.CHANGES_REQUIRED_REMOVE
      contentAction += ` - stage #${cleanLogData.stage_order} - ${cleanLogData.stage_name}`
    }
    else if (logType == ChangeLogType.STAGE_ASSIGNMENT) {
      contentType = ContentType.WORKFLOW_STAGE;
      contentAction = `${ActionType.CHANGE_ASSIGNMENT} stage #${cleanLogData.stage_order} - ${cleanLogData.stage_name}`
    }
    else if (logType == ChangeLogType.STAGE_TRANSITION) {
      contentType = ContentType.WORKFLOW_STAGE;
      contentAction = `${ActionType.MOVE_TO} stage #${cleanLogData.stage_order} - ${cleanLogData.stage_name}`
    }
    else if (logType == ChangeLogType.GRAPHIC_REQUEST_CREATION) {
      contentType = ContentType.GRAPHIC_REQUEST;
      contentAction = ActionType.GRAPHIC_REQUEST_CREATION + getGraphicReqModifier(cleanLogData)
    }
    else if (logType == ChangeLogType.GRAPHIC_REQUEST_DELETION) {
      contentType = ContentType.GRAPHIC_REQUEST;
      contentAction = ActionType.GRAPHIC_REQUEST_DELETION + getGraphicReqModifier(cleanLogData)
    }
    else if (logType == ChangeLogType.GRAPHIC_REQUEST_STATUS) {
      contentType = ContentType.GRAPHIC_REQUEST;
      if (cleanLogData.is_signed_off) contentAction = ActionType.SIGNED_OFF
      else if (cleanLogData.is_remove_signed_off) contentAction = ActionType.SIGNED_OFF_REMOVE
      else if (cleanLogData.is_approved_high_contrast) contentAction = ActionType.APPROVE_HIGH_CONTRAST
      else if (cleanLogData.is_remove_approved_high_contrast) contentAction = ActionType.APPROVE_HIGH_CONTRAST_REMOVE
      else if (cleanLogData.is_unassign) contentAction = ActionType.UNASSIGN
      else if (cleanLogData.assigned_uid) contentAction = `${ActionType.ASSIGN_TO} ${cleanLogData.assignedUser}`
      else if (cleanLogData.new_status) contentAction = `${ActionType.STATUS_CHANGE} ${this.lang.tra(requestStatusOptions.find(option => option.value == cleanLogData.new_status)?.caption)}`
      contentAction += getGraphicReqModifier(cleanLogData)
    }
    return {contentType, contentAction}

  }
  processDate(rawDate){
    return mtz(rawDate).format(this.lang.tra('datefmt_timestamp'));
  }
  //validates that the range is valid i.e start is before end
  dateRangeValidator(): ValidatorFn {
    return (formGroup: FormGroup): ValidationErrors | null => {
      const startDate = formGroup.get('logFilterStartDate').value;
      const endDate = formGroup.get('logFilterEndDate').value;
  
      // Check if both dates are not null and start date is after end date
      if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
        return { dateRangeError: true }; // Return validation error if start date is after end date
      }
  
      // Check if both dates are null (neither provided)
      if (!startDate && !endDate) {
        return { noDatesProvided: true }; // Return validation error if neither start nor end date is provided
      }
  
      return null; // Return null if validation passes
    };
  }
  isViewSplit():Boolean{
    return this.currEditViewMode === EditViewMode.SPLIT;
  }
  setSplitView($event):void{
    this.currEditViewMode = $event;
  }
  toggleIsComparing():void{
    this.isComparingVersions = !this.isComparingVersions;
  }
  isComparingModalVersionOpen():boolean{
    return this.versionComparison.v1 && this.versionComparison.v2;
  }
  getLatestVersion(){

    if (this.versionComparison.v2.time > this.versionComparison.v1.time){
      return this.versionComparison.v2;
    }
    else{
      return this.versionComparison.v1;
    }
  }
  getOlderVersion(){
    if (this.versionComparison.v2.time > this.versionComparison.v1.time){
      return this.versionComparison.v1;
    }
    else{
      return this.versionComparison.v2;
    }
  }
  closeVersionComparisonModal():void{
    this.versionComparison.v2 = undefined;
  }
  removeVersionToCompare(){
    this.versionComparison.v1 = undefined;
  }
  getVersionIdentifier(item:any):string{
    return `Modified On: ${item.time}\nVersion ID: ${item.versionId}`
  }
}
