<div>
  <h1 *ngIf="content.details.stageTitle">{{content.details.stageTitle}}</h1>
  <h2>Assigned To</h2>
  <div class="list has-background-primary">
    <div class="list-item" *ngFor="let name of content.details.assignedAfter">
      <div class="list-item-title">{{name}}</div>
    </div>
  </div>

  <ng-container *ngIf="content.details.assignedBefore">
    <h2 class="has-text-grey">Previous Assignments</h2>
    <div class="list">
      <div class="list-item" *ngFor="let name of content.details.assignedBefore">
        <div class="list-item-title">{{name}}</div>
      </div>
    </div>
  </ng-container>
</div>



