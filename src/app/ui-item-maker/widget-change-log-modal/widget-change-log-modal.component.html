
<div class="modal-content">

  <h1>{{questionLabel}} - Log</h1>

    <div class="notification is-link is-light">
      <div class="columns is-justify-space-between">
        <div class="column is-narrow">
          <b>Include types:</b>
        </div>
        <div class="column is-narrow" *ngFor="let filter of logQueryFilterSettings; let i = index">
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" [(ngModel)]="filter.checked" (change)="onFilterChange()">
              {{ filter.slug }}
            </label>
          </div>
        </div>
      </div>
    </div>
    <!-- Filter for Dates -->
    <div  style="display: flex; justify-content: space-between;">
      <div class="date-filter">
        <div class="input-container">
          <span>From:</span>
          <input type="datetime-local" [formControl]="formGroup.controls.logFilterStartDate" [(ngModel)]="filterDate.start"/>
        </div>
        <div class="input-container">
          <span>Until:</span>
          <input type="datetime-local" [formControl]="formGroup.controls.logFilterEndDate" [(ngModel)]="filterDate.end"/>
          <p class="error-message" *ngIf="formGroup.errors?.dateRangeError">Ensure Range is Valid</p>
        </div>
        <div class="button-container">
          <input type="button" value="Filter" class="filter-button" (click)="filterByDate()" [disabled]="formGroup.status!=='VALID'">
        </div>
        <div class="button-container">
          <input type="button" value="Clear" class="filter-button" (click)="clearDateFilters()">
        </div>
        
      </div>
      <div class="comparisonInput">
        <p *ngIf="versionComparison.v1" class="custom-button">
          Comparing: {{versionComparison.v1.time}}
          <i class="fas fa-times close-icon" (click)="removeVersionToCompare()"></i>
        </p>
        <input class="close-button" type="button" [value]="isComparingVersions ? 'Cancel' : 'Compare Versions'" (click)="toggleIsComparing()">
      </div>

    </div>
    
    
  <div class="space-between align-top">

    <div style="flex-grow: 1;" [class.isSplitView]="isViewSplit()">
      <ag-grid-angular
        class="ag-theme-alpine"
        style="flex-grow: 1; height: 55vh"
        [gridOptions]="changeLogGridOptions"
        (rowSelected)="onRowSelected($event)"
        [enableCellTextSelection]="true"
        [pagination]=true
        (gridReady)="onGridReady($event)"
        (paginationChanged)="onPaginationChanged($event)"
      ></ag-grid-angular>
    </div>


    <div *ngIf="currContent && currContent.isExpandView" class="item-view" [class.isSplitView]="isViewSplit()">
      <div class="review-section">
        <button 
          class="close" 
          (click)="resetSelected()"> 
            <i class="fas fa-times"></i> 
        </button>
      </div>
      <display-config-diff *ngIf="isConfigDiff(currContent)" (isSplitViewEvent)="setSplitView($event)" [content]="currContent"></display-config-diff>
      <display-assignees-diff *ngIf="isAssigneeDiff(currContent)" [content]="currContent"></display-assignees-diff>

    </div>
  </div>
  <!-- This is the modal that pops up when comparing version -->
  <div *ngIf="isComparingModalVersionOpen()" class = 'custom-modal-version-compare'>
    <div class = 'modal-content'>
      <div class="review-section">
        <button 
          class="close" 
          (click)="closeVersionComparisonModal()"> 
            <i class="fas fa-times"></i> 
        </button>
      </div>
      <div class="question-container">
        <div class="divider-container">
          <div class="divider">
            <div class="label" style="color: dimgray;">{{getVersionIdentifier(getOlderVersion())}}</div>
            <div class="input-container">
            </div>
          </div>
          <div class="divider">
            <div class="label">Latest: {{getVersionIdentifier(getLatestVersion())}}</div>
            <div class="input-container">
            </div>
            
          </div>
        </div>
        <div class="question-runner-container">
          <div class="question-runner-left">
            <question-runner 
              [currentQuestion]="getOlderVersion()" 
              [questionState]="{}"
            ></question-runner>
          </div>
          <div class="question-runner-right"  >
            <question-runner 
            [currentQuestion]="getLatestVersion()" 
            [questionState]="{}"
            ></question-runner>
          </div>
        </div>
    </div>
    </div>
  </div>
</div>

