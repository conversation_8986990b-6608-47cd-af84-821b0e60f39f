import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { WidgetChangeLogModalComponent } from './widget-change-log-modal.component';

describe('WidgetChangeLogModalComponent', () => {
  let component: WidgetChangeLogModalComponent;
  let fixture: ComponentFixture<WidgetChangeLogModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ WidgetChangeLogModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(WidgetChangeLogModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
