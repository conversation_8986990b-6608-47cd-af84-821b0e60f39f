@import '../widget-authoring-main/widget-authoring-main.component.scss';
@import '../../../styles/partials/_modal.scss';
@import './display-config-diff/display-config-diff.component.scss';

.custom-modal-version-compare {
  @extend %custom-modal;

  .modal-content {
    flex-direction: column;
    background-color: white;
    max-width: 95%;
    display: flex; /* Add flex display to center child elements */
    align-items: center; /* Center child elements horizontally */

  }

  .question-container {
    height: fit-content;
    width: 90%; /* Adjust the width as needed */
  }
  .review-section {
    width: 100%;
    position: relative;
    padding: 0.5em;
    margin-bottom: 1em;
  }
  .close{
    position: absolute;
    top: 1px;
    right: 1px;
  }
}

.modal-content {
  height: fit-content;
  width: 100%;
}
.date-filter {
  display: flex;
  flex-direction: row;
  margin-bottom: 2em;
  align-items: flex-end;
}

.input-container {
  display: flex;
  flex-direction: column;
  margin-right: 20px;
}

.input-container span {
  margin-bottom: 5px;
}

.button-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end; /* Align button with inputs */
}

.filter-button {
  align-self: center;
  padding: 0.5em 1em;
  margin-right: 1.5em;
}

.error-message {
  margin: auto;
  margin-top: -2em;
  color: red;
  width: 100%;
  font-size: smaller;
}
.timeline-container {
  height: 75vh;
  overflow-y: scroll;
}

.timeline{
  width: 100%;
  display: flex;
  flex-direction: row;
}


.item-view {
  overflow:auto;
  width: 65%;
  height:65vh;
  border: 0.1em solid #d3d3d3;
  margin-left: 1em;
  padding: 10px;
}

.review-section {
  padding: 0.5em;
  margin-bottom: 1em;
}

.close {
  float:right;
  color:black;
  border: none;
  background: transparent;
  cursor: pointer;
  font-weight: bolder;

  i {
    font-size: 1.2rem;
  }
}

//the following is so that the diff view gets bigger when in splitscreen mode 
.isSplitView .ag-theme-alpine{
  flex-grow: 0.5;
}
.isSplitView.item-view{
  flex-grow: 4;
}
//this is for the comparison being inputed
.comparisonInput{
  display: flex;
  order: 2; 
  align-self: center; 
  flex-direction: row;
}
.custom-button {
  position: relative;
  padding: 5px 10px; /* Adjust padding as needed */
  border: 1px solid #ccc; /* Add border style */
  background-color: #f0f0f0; /* Add background color */
  margin-right: 4px;
}
.close-icon {
  cursor: pointer;
}

/* Optional hover effect for the 'x' icon */
.close-icon:hover {
  color: dimgrey; /* Change color on hover */
}