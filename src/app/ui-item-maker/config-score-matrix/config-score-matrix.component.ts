import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { refreshScoreMatrix, MatrixHeaderType } from './models'
import { TwiddleState } from '../../ui-partial/twiddle/twiddle.component';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { LangService } from 'src/app/core/lang.service';

const REFRESH_INTERVAL = 5 * 1000 // 10 secs in ms

@Component({
  selector: 'config-score-matrix',
  templateUrl: './config-score-matrix.component.html',
  styleUrls: ['./config-score-matrix.component.scss']
})
export class ConfigScoreMatrixComponent implements OnInit {

  @Input() element;
  @Output() scoreMatrixConfirmed = new EventEmitter<boolean>();


  isEditing:boolean = false;
  showMatrixTwiddle = new TwiddleState(true);
  refreshScoreMatrix = refreshScoreMatrix;
  cellValueOptions : (number | null)[]  = [
    null, 0, 1
  ];
  cellValueDictionary = {
    1: this.lang.tra("score_matrix_cell_correct"),
    0: this.lang.tra("score_matrix_cell_incorrect")
  }
  MatrixHeaderType = MatrixHeaderType;
  refreshInterval;
  mathObject = '';
  mathProp = '';

  constructor(
    public lang: LangService
  ) { }

  ngOnInit(): void {
    this.refreshScoreMatrix(this.element)
    this.startRefreshInterval();
  }

  ngOnDestroy(): void {
    this.clearRefreshInterval();
  }

  toggleIsEditing() {
    // If starting to edit, refresh first
    if (!this.isEditing) refreshScoreMatrix(this.element)
    this.isEditing = !this.isEditing;
  }

  private startRefreshInterval(): void {
    this.refreshInterval = setInterval(() => {
      this.refreshScoreMatrix(this.element);
    }, REFRESH_INTERVAL);
  }

  private clearRefreshInterval(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
  }
  toggleMatrixConfirmation() {
    this.refreshScoreMatrix(this.element, true);
    this.element.scoreMatrix.isConfirmed = !this.element.scoreMatrix.isConfirmed;
    this.scoreMatrixConfirmed.emit(this.element.scoreMatrix.isConfirmed);
  
  }

}
