import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { WorkflowGroupAssignmentModalComponent } from './workflow-group-assignment-modal.component';

describe('WorkflowGroupAssignmentModalComponent', () => {
  let component: WorkflowGroupAssignmentModalComponent;
  let fixture: ComponentFixture<WorkflowGroupAssignmentModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ WorkflowGroupAssignmentModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(WorkflowGroupAssignmentModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
