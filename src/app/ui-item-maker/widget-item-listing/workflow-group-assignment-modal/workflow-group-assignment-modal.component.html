<menu-bar 
  [menuTabs]="stageTabs"
  [tabIdInit]="selectedStage.order"
  (change)="resetStage($event)">
</menu-bar>

<div class="content">
  <div class="table-container">
    <table class="table is-hoverable is-bordered">
      <thead>
        <tr>
          <th class="is-narrow has-text-centered">
            <label>
              <input 
                type="checkbox" 
                id="selectAll" 
                name="selectAll" 
                (click)="toggleAllQuestions(!checkCurrentStageToggle())" 
                [checked]="checkCurrentStageToggle()">
              Select
            </label>          
          </th>
          <th class="is-narrow has-text-centered">Item ID</th>
          <th>Item Label</th>
          <th>Current Assignees</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let question of questionInfoByStageOrder[selectedStage.order]">
          <td><input type="checkbox" [(ngModel)]="question.isSelected" (change)="checkToggleStatus()"/></td>
          <td class="has-text-centered">{{question.id}}</td>
          <td>{{question.label}}</td>
          <td>{{question.currAssignees}}</td>
        </tr>
      </tbody>
    </table>
  </div>
  
   <!-- User assignment -->
  <div *ngIf="!isItemBankUserLoading" class="assignment-container">
    <chip-selector
    [potentialItems]="potentialAssignees" 
    [selectedItems]="selectedAssignees"
    [itemIdProp]="'uid'"
    [caption]="'auth_workflow_assign_users'"
    [placeholder]="'auth_workflow_new_users'"
    [isDisabled]="false"
    [renderItemOption]="renderAssigneeOption"
    [renderItemChip]="renderAssigneeChip"
    [filterItems]="filterAssignees"
    (selectionChanged)="setAssignmentTouched()"
    >
    </chip-selector> 

    <!-- Save user assignment -->
    <div class="columns">
      <div class="column is-one-quarter">
        <button 
          (click)="initSaveAssignment()"
          class="button is-small is-link is-fullwidth"
          [class.is-loading]="currAssignmentState==AssignmentState.SENDING"
          [class.is-success]="currAssignmentState==AssignmentState.SENT"
          [class.is-danger]="currAssignmentState==AssignmentState.ERROR"
          [disabled]="!isAssignmentTouched || !selectedAssignees.length"
        >
          <tra [slug]="getSaveAssignmentSlug()"></tra>
        </button>
      </div>
      <div class="column is-one-quarter">
        <button 
          (click)="initRemoveAssignments()"
          class="button is-small is-link is-fullwidth"
          [class.is-loading]="currDeleteAssignmentState==AssignmentState.SENDING"
          [class.is-success]="currDeleteAssignmentState==AssignmentState.SENT"
          [class.is-danger]="currDeleteAssignmentState==AssignmentState.ERROR"
          [disabled]="getDeleteButtonIsDisabled()"
        >
          <tra [slug]="getDeleteAssignmentSlug()"></tra>
        </button>
      </div>
    </div>
  </div>
  
</div>

