import { Component, OnInit, Input } from '@angular/core';
import { IMenuTabConfig } from 'src/app/ui-partial/menu-bar/menu-bar.component';
import { ItemBankCtrl } from '../../item-set-editor/controllers/item-bank';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { AuthRolesService } from '../../auth-roles.service';
import { IStageAssignee, IWorkflowStage } from './../../item-workflow-section/model'
import { renderAssigneeChip, renderAssigneeOption, filterAssignees } from './../../item-workflow-section/functions'
import { AssignmentState } from './../../../ui-scoring-leader/panel-sl-new-assignment/panel-sl-new-assignment.component'
import { LangService } from 'src/app/core/lang.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';

@Component({
  selector: 'workflow-group-assignment-modal',
  templateUrl: './workflow-group-assignment-modal.component.html',
  styleUrls: ['./workflow-group-assignment-modal.component.scss']
})
export class WorkflowGroupAssignmentModalComponent implements OnInit {



  @Input() itemBankCtrl:ItemBankCtrl
  @Input() questionStages: IWorkflowStage[];
  @Input() questionInfoByStageOrder;

  stageTabs: IMenuTabConfig<number>[];

  allItemBankUsers:IStageAssignee[] = [];
  potentialAssignees:IStageAssignee[] = [];
  selectedAssignees:IStageAssignee[] = [];
  isAssignmentTouched: boolean;
  isItemBankUserLoading:boolean;
  assignSelectorTrigger: boolean;

  AssignmentState = AssignmentState
  currAssignmentState:AssignmentState = AssignmentState.IDLE;
  currDeleteAssignmentState: AssignmentState = AssignmentState.IDLE;
  selectedStage: IWorkflowStage;

  allItemsSelected : boolean[];
  renderAssigneeOption = renderAssigneeOption;
  renderAssigneeChip = renderAssigneeChip;
  filterAssignees = filterAssignees;

  constructor(
    private lang: LangService,
    private auth: AuthService,
    private routes: RoutesService,
    private authRoles: AuthRolesService,
    private loginGuard: LoginGuardService,
  ) {}

  
  /** When another stage chosen - store selected stage, reset users available for assignment, clear assignee input */
  resetStage($event){
    this.selectedStage = this.questionStages.find(stage => stage.order == $event);
    this.resetPotentialAssignees();
    this.selectedAssignees = [];
  }

  async ngOnInit(): Promise<void> {
    this.allItemsSelected = Array.from({ length: this.questionStages.length }).fill(true) as boolean[];

    this.initStageTabs();
    await this.loadItemBankUsers();
    this.resetPotentialAssignees();
    this.selectedAssignees = []
  }

  /** Create tabs based on available stages passed, select the first one */
  initStageTabs(){
    this.stageTabs = this.questionStages.map(stage => {
      return {
        id: stage.order,
        caption: `${stage.order}. ${this.lang.tra(stage.slug)}`
      }
    })
    this.selectedStage = this.questionStages[0]
  }
  /** Toggle all Items */
  toggleAllQuestions(selected:boolean){
    const stage = this.selectedStage.order;
    this.questionInfoByStageOrder[stage].forEach(question =>{
      question.isSelected = selected;
    })
    this.allItemsSelected[stage - 1] = selected;
  }
  /** Check The toggle State of current Tab */ 
  checkCurrentStageToggle(){
    if(this.allItemsSelected[this.selectedStage.order - 1]===undefined){
      this.allItemsSelected[this.selectedStage.order - 1] = true;
    }
    return this.allItemsSelected[this.selectedStage.order - 1];
  }
  /** Check If all the items in a Stage are selected or if all are not selected */
  checkToggleStatus() {
    const allSelectedTrue = this.questionInfoByStageOrder[this.selectedStage.order].every(question => question.isSelected === true);
    const allSelectedFalse = this.questionInfoByStageOrder[this.selectedStage.order].every(question => question.isSelected === false);
    if(allSelectedTrue){
      this.allItemsSelected[this.selectedStage.order - 1] = true;
    } else if (allSelectedFalse){
      this.allItemsSelected[this.selectedStage.order - 1] = false;
    }
  }
  /** Get all the users of this item bank */
  async loadItemBankUsers(){
    this.isItemBankUserLoading = true;
    const {groupId, single_groupId} = this.itemBankCtrl;
    const query = {
      single_group_id: single_groupId,
      group_id: groupId,
      auth_group_ids: single_groupId ? [groupId, single_groupId] : groupId,
      getAuthEmails: true
    }

    return this.auth.apiFind(this.routes.TEST_AUTH_GROUP_MEMBERS, {query})
    .then((res: any[]) => {
      res.forEach(r => {
        this.allItemBankUsers.push({
          uid: r.id, 
          first_name: r.first_name,
          last_name: r.last_name,
          contact_email: r.contact_email,
          actual_role: this.authRoles.determineRoleFromRoleTypes(r.role_types)
        })
      })
    })
    .finally(() => {
      this.isItemBankUserLoading = false;
    })

  }

  /** From all item bank users, only show the ones with user roles specifies for current stage in the assignment options */
  resetPotentialAssignees(){
    this.potentialAssignees = this.allItemBankUsers.filter(user => {
      return this.selectedStage.assigneeRoles.includes(user.actual_role)
    })
    this.assignSelectorTrigger = !this.assignSelectorTrigger
  }

  setAssignmentTouched(){
    this.isAssignmentTouched = true;
  }

  async initSaveAssignment(){
    if (this.currAssignmentState !== AssignmentState.IDLE) return;
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('auth_workflow_save_assignment_confirm'),
      confirm: () => {
        this.saveAssignment()
      }
    });
  }

  async initRemoveAssignments(){
    if (this.currDeleteAssignmentState !== AssignmentState.IDLE) return;
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('Are you sure you want to remove this assignment?'),
      confirm: () => {
        this.removeAssignments()
      }
    });
  }

  /** Remove selected assigness or all assignees if none selected*/
  async removeAssignments(){
    const selectedStageOrder = this.selectedStage.order
    const query = { lang: this.lang.c(), stage_order: selectedStageOrder, remove_assignees: 1}
    const targetquestionIds = this.questionInfoByStageOrder[selectedStageOrder].filter(question => question.isSelected).map(question => question.id);
    const data = {
      test_question_ids: targetquestionIds,
    }
    this.currDeleteAssignmentState = AssignmentState.SENDING;

    this.auth.apiUpdate(this.routes.TEST_AUTH_QUESTION_WORKFLOW_STAGES, null, data, {query})
    .then(() => {
      this.currDeleteAssignmentState = AssignmentState.SENT;
      // Update assignees on client
      targetquestionIds.forEach(qId => {
        this.itemBankCtrl.setWorkflowStageAssignees([], qId)
        const question = this.questionInfoByStageOrder[selectedStageOrder].find(q => q.id == qId)
        question.currAssignees = this.itemBankCtrl.renderQuestionStageAssignees(qId)
      })

    })
    .catch(() => {
      this.currDeleteAssignmentState = AssignmentState.ERROR;
    })
    .finally(() => {
      this.isAssignmentTouched = false;
      // Displayed success/error for 1.5secs only, then return to normal
      setTimeout(() => {
        this.currDeleteAssignmentState = AssignmentState.IDLE;
      }, 1500);
    })
  }
  /** Save assignments selected in the input*/
  async saveAssignment(){
    const selectedStageOrder = this.selectedStage.order
    const query = { lang: this.lang.c(), stage_order: selectedStageOrder }
    const targetquestionIds = this.questionInfoByStageOrder[selectedStageOrder].filter(question => question.isSelected).map(question => question.id);
    const data = {
      test_question_ids: targetquestionIds,
      assigned_uids: this.selectedAssignees.map(user => user.uid)
    }
    this.currAssignmentState = AssignmentState.SENDING;

    this.auth.apiUpdate(this.routes.TEST_AUTH_QUESTION_WORKFLOW_STAGES, null, data, {query})
    .then(() => {
      this.currAssignmentState = AssignmentState.SENT;
      // Update assignees on client
      targetquestionIds.forEach(qId => {
        this.itemBankCtrl.setWorkflowStageAssignees(this.selectedAssignees, qId)
        const question = this.questionInfoByStageOrder[selectedStageOrder].find(q => q.id == qId)
        question.currAssignees = this.itemBankCtrl.renderQuestionStageAssignees(qId)
      })

    })
    .catch(() => {
      this.currAssignmentState = AssignmentState.ERROR;
    })
    .finally(() => {
      this.isAssignmentTouched = false;
      // Displayed success/error for 1.5secs only, then return to normal
      setTimeout(() => {
        this.currAssignmentState = AssignmentState.IDLE;
      }, 1500);
    })
  }

  getSaveAssignmentSlug(){
    if (this.currAssignmentState==AssignmentState.SENT) return "btn_success"
    else if (this.currAssignmentState==AssignmentState.IDLE) return "auth_workflow_save_assignment"
    else if (this.currAssignmentState==AssignmentState.ERROR) return "btn_error"
  }
  getDeleteAssignmentSlug(){
    if (this.currDeleteAssignmentState==AssignmentState.SENT) return "btn_success"
    else if (this.currDeleteAssignmentState==AssignmentState.IDLE ) return "Remove Assignees"
    else if (this.currDeleteAssignmentState==AssignmentState.ERROR) return "btn_error"
  }

  getDeleteButtonIsDisabled(){
    const selectedHaveAssigness = this.questionInfoByStageOrder?.[this.selectedStage.order]?.some(qInfo =>{
      return qInfo.isSelected && qInfo.currAssignees?.length
    })
    return !selectedHaveAssigness
  }
}
