<div style="display: flex; justify-content: space-between;">
  <div class="pagination">
    <button (click)="itemFilterCtrl.prevItemListPage()" class="button is-small"><tra slug="ie_prev"></tra></button>
    <button (click)="itemFilterCtrl.nextItemListPage()" class="button is-small"><tra slug="ie_next"></tra></button>
    <span><tra slug="ie_page"></tra></span>
    <!-- <input class="input is-small"> -->
    <span>{{itemBankCtrl.currentItemListPage}}</span>
    <span>of {{itemBankCtrl.totalItemListPages}}</span>
    <span style="margin-left:1em;">
      <span *ngIf="isParamTypeActive(ItemParameterType.STRUCT)">
        <button (click)="reloadExpectedAnswers()" class="button is-small" [disabled]="isEaLoading">
          <span *ngIf="isEaLoaded">Re</span>load EA</button>
      </span>
      <label>
        <input type="checkbox" [formControl]="itemFilterCtrl.isQuestionFiltersActive">
        <span  style="margin-left:0.5em">
          <tra slug="ie_activate_filter_search"></tra>
        </span>
      </label>
      <button [disabled]="isReadOnly()" (click)="itemFilterCtrl.clearFilter()" class="button is-small"><tra slug="ie_clear_filter"></tra></button>
      <button [disabled]="isReadOnly()" (click)="saveFilter()" class="button is-small"><tra slug="auth_save_filter"></tra></button>
      <button [disabled]="!hasSavedFilters()" [class.is-info]="showSavedFilters && hasSavedFilters()" (click)="viewSavedFilters()" class="button is-small"><tra slug="auth_view_save_filters"></tra></button>
    </span>
    <span style="margin-left:1em;" *ngIf="false">
      <tra slug="ie_show_parameters_as_of"></tra>
      <input [disabled]="isReadOnly()" type="date" class="input is-small" style="width: 12em;">
    </span>
    <span>
      Parameters: 
      <button 
        *ngFor="let paramType of parameterTypes"
        class="button is-small"
        [class.is-info]="isParamTypeActive(paramType.slug)"
        (click)="toggleParamTypeActive(paramType.slug)"
      >
      {{paramType.caption}}
      </button>
    </span>
  </div>
  <div>
    <button [disabled]="isReadOnly()" (click)="frameworkCtrl.openImport()" class="button is-small">
      <tra slug="ie_import_parameters"></tra>
    </button>
    <button (click)="frameworkCtrl.exportTable(excludeHidden)" class="button is-small has-icon">
      <span class="icon"><i class="fa fa-table" aria-hidden="true"></i> </span>
      <span><tra slug="ie_export_items_to_spreadsheet"></tra></span>
    </button>
  </div>
</div>
<div class="toggle-parameter">
  <mat-slide-toggle [(ngModel)]="excludeHidden"><tra slug="ie_exclude_hidden_params_from_export"></tra></mat-slide-toggle>
</div>
<div *ngIf="isParamTypeActive(ItemParameterType.HISTORICAL)" style="display: flex; flex-direction: row;">
  <div>
    <strong>Test Windows</strong>
    <div class="tw-td-listing-container">
      <div *ngFor="let tw of getHistoricalTw()">
        <button 
          class="button is-small"
          [class.is-info]="tw.is_selected"
          (click)="toggleHistoricalTwSelection(tw)"
        >
          {{tw.title}}
        </button>
      </div>
    </div>
  </div>
  <div>
    <strong>Test Designs</strong>
    <div class="tw-td-listing-container">
      <ng-container *ngFor="let tw of getHistoricalTw()">
        <ng-container *ngIf="tw.is_selected">
          <div *ngFor="let td of tw.test_designs">
            <button 
              class="button is-small"
              [class.is-info]="isHistoricalTdSelected(td.slug)"
              (click)="toggleHistoricalTdSelection(td.slug)"
            >
              {{td.caption}}
            </button>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>
</div>

<div class="saved-filters" *ngIf="showSavedFilters && hasSavedFilters()">
  <table>
    <tr *ngFor="let filter of frameworkCtrl.asmtFmrk.filters; let filterIndex = index">
      <td>
        <div class="filter-container">
          <div class="filter-name">{{filter.name}}</div>
          <button [disabled]="isReadOnly()" (click)="removeSavedFilter(filterIndex)" [title]="lang.tra('Remove Filter')" class="button is-small is-danger">
            <i class="fa fa-minus" aria-hidden="true"></i>
          </button>
          <button (click)="applySavedFilter(filter)" [title]="lang.tra('Apply Filter')" class="button  is-small">
            <i class="fa fa-eye" aria-hidden="true"></i>
          </button>
        </div>
      </td>
    </tr>
  </table>
</div>
<ng-container *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">

  <div>
    <div class="field has-addons" style="margin-bottom: 0; width: 25%">
      <div class="control" style="width: 80%">
        <input #tagInput 
        matInput 
        (keydown.enter)="itemFilterCtrl.updateItemFilter()" 
        class="input" 
        type="text" 
        [placeholder]="lang.tra('Filter by tags/content')" 
        [formControl]="itemFilterCtrl.searchQuery"
        [matAutocomplete]="auto">
      </div>
      <div class="control" style="width: 20%">
        <a class="button is-info" (click)="itemFilterCtrl.updateItemFilter()">
          <i class="fa fa-search"></i>
        </a>
      </div>
    </div>
    <mat-autocomplete #auto="matAutocomplete">
      <mat-option *ngFor="let tag of itemFilterCtrl.filteredTags | async" (click)="addTag(tag)" [value]="tag.caption">
        {{tag.caption}}
      </mat-option>
    </mat-autocomplete>
  </div>
  <div style="display: flex; flex-wrap: wrap">
    <ng-container *ngFor="let tag of itemFilterCtrl.filteringTags"> 
        <item-tag (delete)="itemFilterCtrl.deleteTag(tag)" [tag]="tag"></item-tag>
    </ng-container>
  </div>
</ng-container>

<div *ngIf="testletCtrl.activeTestlet && testletCtrl.activeTestlet.questionsMissing && testletCtrl.activeTestlet.questionsMissing.length">
  The following questions have been unlinked from the item bank and must be replaced in the testlet:
  <table style="width:auto;">
    <tr>
      <th>ID</th>
      <th>Label</th>
      <th>Action</th>
    </tr>
    <tr *ngFor="let questionRef of testletCtrl.activeTestlet.questionsMissing">
      <td>{{questionRef.id}}</td>
      <td>{{questionRef.label}}</td>
      <td><button (click)="testletCtrl.replaceTestletItem(testletCtrl.activeTestlet.testlet, questionRef.label)">Swap</button></td>
    </tr>
  </table>
</div>

<div #questionListing class="item-detail-review">
  <div class="item-table-container" [class.is-shared-w]="itemBankCtrl.currentQuestion" *ngIf="!frameworkCtrl.isImportFlushing">
    <!-- CLOSER_LOOK_20210807 start -- this big block was not in the incoming branch -->
    <div style="padding-left:25em; max-width:100%; overflow:auto">
      <table #parameterTable class="table item-table is-bordered is-condensed">
        <tr >
          <th style="white-space: nowrap;">
            <span>Item ID</span>
            <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
              <a (click)="addNewUserFilter(itemFilterCtrl.filterSettings, 'id')" style="white-space: nowrap;">
                <i class="fa fa-search"></i>
                {{itemFilterCtrl.filterSettings['id']}}
              </a>
            </div>
          </th>
          <ng-container *ngIf="isParamTypeActive(ItemParameterType.STRUCT)">
            <th style="white-space: nowrap;">
              <button 
                class="button is-light is-small"
                (click)="toggleExcludeBlankItemVersions()"
                [class.is-info]="itemFilterCtrl.isExcludeBlankItemVersions"
              >
                Item Version
              </button>
              <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
                <a (click)="addNewUserFilter(itemFilterCtrl.filterSettings, 'item_version_code')" style="white-space: nowrap;">
                  <i class="fa fa-search"></i>
                  {{itemFilterCtrl.filterSettings['item_version_code']}}
                </a>
              </div>
            </th>
            <th style="white-space: nowrap;">
              <div>Item Set</div>
              <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
                <a (click)="addNewUserFilter(itemFilterCtrl.filterSettings, 'question_set_id')" style="white-space: nowrap;">
                  <i class="fa fa-search"></i>
                  {{itemFilterCtrl.filterSettings['question_set_id']}}
                </a>
              </div>
            </th> 
  
          <th>
            <span><tra slug="auth_workflow_stage"></tra></span>
            <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
              <a (click)="addNewUserFilter(itemFilterCtrl.workflowFilterSettings, 'stage')" style="white-space: nowrap;">
                <i class="fa fa-search"></i>
                {{itemFilterCtrl.workflowFilterSettings['stage']}}
              </a>
            </div>
          </th>

          <th>
            <div class="columns">
              <div class="column">
                <span><tra slug="auth_workflow_assignees"></tra></span>
                <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
                  <a (click)="addNewUserFilter(itemFilterCtrl.workflowFilterSettings, 'assignees')" style="white-space: nowrap;">
                    <i class="fa fa-search"></i>
                    {{itemFilterCtrl.workflowFilterSettings['assignees']}}
                  </a>
                </div>
              </div>
              <div class="column is-narrow">
                <button class="button is-rounded is-small is-light" (click)="multiAssignModalStart()">Multi-assign</button>
              </div>
            </div>
          </th>
          
          <th>
            <div class="columns">
              <div class="column">
                <span><tra slug="auth_editor_assignees"></tra></span>
                <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
                  <a (click)="addNewUserFilter(itemFilterCtrl.workflowFilterSettings, 'editors')" style="white-space: nowrap;">
                    <i class="fa fa-search"></i>
                    {{itemFilterCtrl.workflowFilterSettings['editors']}}
                  </a>
                </div>
              </div>
            </div>
          </th>

          <th style="white-space: nowrap;">
              <span><tra slug="auth_graphic_req_pending"></tra></span>
              <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
                <a (click)="addNewUserFilter(itemFilterCtrl.workflowFilterSettings, 'graphicReqPending')" style="white-space: nowrap;">
                  <i class="fa fa-search"></i>
                  {{itemFilterCtrl.workflowFilterSettings['graphicReqPending']}}
                </a>
              </div>
            </th>
            <th  *ngIf="frameworkCtrl.asmtFmrk.isExposureComputed && frameworkCtrl.isTestformLOFT()" style="white-space: nowrap;">
              <tra slug="ie_no_testlets_enabled"></tra>
            </th>
            <th  *ngIf="frameworkCtrl.asmtFmrk.isExposureComputed && frameworkCtrl.isTestformLOFT()" style="white-space: nowrap;">
              <tra slug="ie_est_exp"></tra>
            </th>
            <th style="white-space: nowrap;">
              <div>Is Questionnaire?</div>
              <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
                <select [(ngModel)]="itemFilterCtrl.filterQuestionnaireFlag" (change)="itemFilterCtrl.updateItemFilter()">
                  <option></option>
                  <option value="yes">Yes</option>
                  <option value="no">No</option>
                </select>
              </div>
            </th>
            <th style="white-space: nowrap;">
              <div>Is Resource?</div>
              <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
                <select [(ngModel)]="itemFilterCtrl.filterResourceFlag" (change)="itemFilterCtrl.updateItemFilter()">
                  <option></option>
                  <option value="yes">Yes</option>
                  <option value="no">No</option>
                </select>
              </div>
            </th>
            <th style="white-space: nowrap;">Assigned Resource</th>
            <ng-container *ngIf="isEaLoaded">
              <th style="white-space: nowrap;">Expected Answer</th>
              <th style="white-space: nowrap;">Weight</th>
            </ng-container>
          </ng-container>
          <ng-container *ngIf="isParamTypeActive(ItemParameterType.CONTENT)">
            <ng-container *ngIf="frameworkCtrl.asmtFmrk.standardParameters">
              <th *ngFor="let param of frameworkCtrl.asmtFmrk.standardParameters" 
                [style.display]="getParamDisplay(param)"
                [ngStyle]="{'background-color':frameworkCtrl.parseParamColor(param.color)}"
                title="{{param.code}} {{param.name}}"
              >
                {{param.code}}
                <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
                  <a [class.no-pointer-events]="isReadOnly()" (click)="filterParams(param)" style="white-space: nowrap;">
                    <i class="fa fa-search"></i>
                    {{renderFilter(param)}}
                  </a>
                </div>
              </th>
            </ng-container>
            <th *ngFor="let param of frameworkCtrl.asmtFmrk.primaryDimensions" 
            [style.display]="getParamDisplay(param)"
            [ngStyle]="{'background-color':frameworkCtrl.parseParamColor(param.color)}"
            title="{{param.code}} {{param.name}}"
            >
              {{param.code}}
              <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
                <a [class.no-pointer-events]="isNotSearchable(param)" (click)="filterParams(param)" style="white-space: nowrap;">
                  <i class="fa fa-search"></i>
                  {{renderFilter(param)}}
                </a>
              </div>
            </th>
            <th *ngFor="let param of frameworkCtrl.asmtFmrk.secondaryDimensions" 
            [style.display]="getParamDisplay(param)"
            [ngStyle]="{'background-color':frameworkCtrl.parseParamColor(param.color)}"
            title="{{param.code}} {{param.name}}"
            >
              {{param.code}}
              <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
                <a [class.no-pointer-events]="isNotSearchable(param)" (click)="filterParams(param)" style="white-space: nowrap;">
                  <i class="fa fa-search"></i>
                  {{renderFilter(param)}}
                </a>
              </div>
            </th>
          </ng-container>
          
          <ng-container *ngIf="isParamTypeActive(ItemParameterType.HISTORICAL)">
            <th *ngFor="let prop of itemBankCtrl.itemRegisterSummary.historicalProps">
              {{prop.caption}}
            </th>
          </ng-container>
          <ng-container *ngIf="isParamTypeActive(ItemParameterType.STATS)">
            <td class="param-no-wrap" *ngFor="let prop of itemBankCtrl.psychStatsProps">
              {{prop}}
            </td>
          </ng-container>
        </tr>
        <tr *ngFor="let question of itemBankCtrl.itemList; let questionIndex = index" [class.is-sequence-row]="question.type === ItemType.SEQUENCE">
          <td style="white-space: nowrap;">
            {{question.id}}
          </td>
          <ng-container *ngIf="isParamTypeActive(ItemParameterType.STRUCT)">
            
            <td style="white-space: nowrap;">
              {{question.item_version_code}}
            </td>
            <td>{{question.question_set_id}}</td>
  
          <td style="white-space: nowrap;">
            <span>{{itemBankCtrl.renderQuestionStageFull(question.id)}}</span>
            <span *ngIf="itemBankCtrl.getQuestionStage(question.id)?.isEditStage">&nbsp;(#{{itemBankCtrl.getNumTimesEnterEdit(question.id)}})</span>
          </td>
          <td style="white-space: nowrap;"> {{itemBankCtrl.renderQuestionStageAssignees(question.id)}}</td>
          <td style="white-space: nowrap;"> {{itemBankCtrl.renderQuestionEditorAssignees(question.id)}}</td>
          <td style="white-space: nowrap;" class="has-text-centered">
              <span *ngIf="itemBankCtrl.getNumPendingGraphicReqs(question.id)">
                <i class="fa fa-check" style="font-size: 0.6em"></i>
                ({{itemBankCtrl.getNumPendingGraphicReqs(question.id)}})
              </span>
            </td>
  
          <td *ngIf="frameworkCtrl.asmtFmrk.isExposureComputed && frameworkCtrl.isTestformLOFT()">
              {{testletCtrl.questionTestletInfo.get(+question.id)?.quadrantFreq || 0}}
            </td>
            <td *ngIf="frameworkCtrl.asmtFmrk.isExposureComputed && frameworkCtrl.isTestformLOFT()">
              {{testletCtrl.questionTestletInfo.get(+question.id)?.estimatedExposure || 0}}
            </td>
            <td>
              <span class="tag" *ngIf="itemEditCtrl.isCurrentEnabledQuestion(question) || itemBankCtrl.getQuestionContent(question).isQuestionnaire">
                <input *ngIf="isColEnabled(question)" type="checkbox"  [(ngModel)]="itemBankCtrl.getQuestionContent(question).isQuestionnaire" [disabled]="itemBankCtrl.isContentEditingDisabled()">
                <i *ngIf="!isColEnabled(question) && itemBankCtrl.getQuestionContent(question).isQuestionnaire" class="fa fa-check" style="font-size: 0.6em;"></i>
              </span>
            </td>
            <td>
              <span class="tag" *ngIf="itemEditCtrl.isCurrentEnabledQuestion(question) || itemBankCtrl.getQuestionContent(question).isReadingSelectionPage">
                <ng-container *ngIf="isColEnabled(question)">
                  <input type="checkbox"  [(ngModel)]="itemBankCtrl.getQuestionContent(question).isReadingSelectionPage" [disabled]="itemBankCtrl.isContentEditingDisabled()">
                </ng-container>
                <i *ngIf="!isColEnabled(question) && itemBankCtrl.getQuestionContent(question).isReadingSelectionPage" class="fa fa-check" style="font-size: 0.6em;"></i>
              </span>
            </td>
            <td style="white-space: nowrap;">
              <button *ngIf="itemEditCtrl.isCurrentEnabledQuestion(question)" (click)="itemEditCtrl.addSelectionByLabel()" [disabled]="itemBankCtrl.isContentEditingDisabled()">
                add
              </button>
              <span 
                *ngFor="let readSel of itemBankCtrl.getReadingSelections( itemBankCtrl.getQuestionContent(question) );"
                class="tag " 
                [class.is-info]="!!itemBankCtrl.getQuestionByLabel(readSel)" 
                [class.is-danger]="!itemBankCtrl.getQuestionByLabel(readSel)" 
              >
                {{readSel}}
              </span>
              <span class="tag" *ngIf="itemEditCtrl.isCurrentEnabledQuestion(question) || question.isStartHalf">
                <input type="checkbox"  [(ngModel)]="question.isStartHalf" [disabled]="!itemEditCtrl.isCurrentEnabledQuestion(question) || itemBankCtrl.isContentEditingDisabled()">
                Open
              </span>
              <span *ngIf="itemEditCtrl.isCurrentEnabledQuestion(question) ">
                <button *ngIf="itemEditCtrl.isCurrentEnabledQuestion(question)" (click)="itemEditCtrl.clearSelections()" [disabled]="itemBankCtrl.isContentEditingDisabled()">
                  clear
                </button>
              </span>
            </td>
            <ng-container *ngIf="isEaLoaded" [ngSwitch]="!!itemBankCtrl.hasExpectedAnswer(question.id)">
              <ng-container *ngSwitchCase="false">
                <td>... </td>
                <td>... </td>
              </ng-container>
              <ng-container *ngSwitchCase="true">
                <td> <!-- Expected Answer -->
                  <select style="max-width: 15em;">
                    <option *ngFor="let option of itemBankCtrl.getEAOptions(question.id)">{{option}}</option>
                  </select>
                </td>
                <td> <!-- Weight -->
                  {{itemBankCtrl.getEAWeight(question.id)}}
                </td>
              </ng-container>
            </ng-container>
          </ng-container>

          <ng-container *ngIf="isParamTypeActive(ItemParameterType.CONTENT)">
            <ng-container *ngIf="frameworkCtrl.asmtFmrk.standardParameters">
              <td *ngFor="let param of frameworkCtrl.asmtFmrk.standardParameters" 
                style="white-space:nowrap;" 
                [style.display]="getParamDisplay(param)"
                [ngStyle]="{'background-color':frameworkCtrl.parseParamColor(param.color)}"
                title="{{param.code}} {{param.name}}"
              >
                <framework-dimension-input [frameworkCtrl]="frameworkCtrl" [param]="param" [question]="question" [questionScoringInfo]="itemBankCtrl.getQuestionScoringInfo(question.id)" [changeCounter]="question.__changeCounter" [enabled]="isDimensionEnabled(question, param)"></framework-dimension-input>
              </td>
            </ng-container>
            <td *ngFor="let param of frameworkCtrl.asmtFmrk.primaryDimensions"  
            style="white-space:nowrap;" 
            [style.display]="getParamDisplay(param)"
            [ngStyle]="{'background-color':frameworkCtrl.parseParamColor(param.color)}"
            title="{{param.code}} {{param.name}}"
            >
              <framework-dimension-input [frameworkCtrl]="frameworkCtrl" [param]="param" [question]="question" [questionScoringInfo]="itemBankCtrl.getQuestionScoringInfo(question.id)" [changeCounter]="question.__changeCounter" [enabled]="isDimensionEnabled(question, param)"></framework-dimension-input>
            </td>
            <td *ngFor="let param of frameworkCtrl.asmtFmrk.secondaryDimensions" 
            style="white-space:nowrap;" 
            [style.display]="getParamDisplay(param)"
            [ngStyle]="{'background-color':frameworkCtrl.parseParamColor(param.color)}"
            title="{{param.code}} {{param.name}}"
            >
              <framework-dimension-input [frameworkCtrl]="frameworkCtrl" [param]="param" [question]="question" [questionScoringInfo]="itemBankCtrl.getQuestionScoringInfo(question.id)" [changeCounter]="question.__changeCounter" [enabled]="isDimensionEnabled(question, param)"></framework-dimension-input>
            </td>
          </ng-container>
          <ng-container *ngIf="isParamTypeActive(ItemParameterType.HISTORICAL)">
            <td class="param-no-wrap" *ngFor="let prop of itemBankCtrl.itemRegisterSummary.historicalProps">
              <ng-container *ngFor="let td of getHistoricalTdSelected()">
                <ng-container *ngIf="isHistoricItemInTd(question.id, td.slug)">
                  <span [title]="td.caption" class="tag">{{historicalPropVal(question.id, td.slug, prop.slug)}}</span>
                </ng-container>
              </ng-container>
            </td>
          </ng-container>
          <ng-container *ngIf="isParamTypeActive(ItemParameterType.STATS)">
            <td class="param-no-wrap" *ngFor="let prop of itemBankCtrl.psychStatsProps">
              <div *ngFor="let record of itemBankCtrl.historicalItemStats.get(question.id + ';' + question.item_version_code)">
                {{record[prop]}}
              </div>
            </td>
          </ng-container>
        </tr>
      </table>
    </div>
<!-- CLOSER_LOOK_20210807 end -->
    <table class="table item-table is-bordered is-condensed" style="position:absolute; width:auto; top:0px; left:0px;     z-index: 10;">
      <tr>
        <th style="width:25em;" [style]="'height: ' + (getRowHeight(-1) - 0.5) + 'px'">
          <tra slug="ie_item"></tra>
          <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
            <a (click)="addNewUserFilter(itemFilterCtrl.filterSettings, 'label')" style="white-space: nowrap;">
              <i class="fa fa-search"></i>
              {{itemFilterCtrl.filterSettings['label']}}
            </a>
          </div>
        </th>
      </tr>
      <tr *ngFor="let question of itemBankCtrl.itemList; let questionIndex = index" [class.is-sequence-row]="question.type === ItemType.SEQUENCE">
        <td 
        [ngStyle]="{'height': getRowHeight(questionIndex) + 'px', 'width': '25em'}"
        >
          <div style="display: flex; flex-direction:row; justify-content: space-between;">
            <div>
              <span style="width: 1em; overflow: hidden; margin-right: 0.3em; display: inline-block; vertical-align: bottom;">
                <i *ngIf="question.isReady" style="color: #82ce82;" class="fa fa-check" aria-hidden="true"></i>
              </span>
              <span style="position:relative;">
                {{question.label}}
                <span class="slide-top" [class.is-active]="itemBankCtrl.isQuestionRecentlyAdded(question)">{{question.label}}</span>
              </span>
            </div>
            <div>
              <ng-container *ngIf="question.type !== ItemType.SEQUENCE">
                <button *ngIf="testletCtrl.activeTestlet" [disabled]="isReadOnly()" class="button  is-small"  (click)="testletCtrl.replaceTestletItem(testletCtrl.activeTestlet.testlet, null, question.id)">
                  <i class="fa fa-crosshairs" aria-hidden="true"></i>
                </button>
                <button *ngIf="panelCtrl.activePanel && panelCtrl.activePanel.moduleId" [disabled]="isReadOnly()" class="button  is-small"  (click)="panelCtrl.replacePanelModuleItem(question.label)">
                  <i class="fa fa-crosshairs" aria-hidden="true"></i>
                </button>
                <button [disabled]="isReadOnly()" (click)="itemBankCtrl.addQuestionToTestForm(question)" [title]="lang.tra('ie_add_item_to_tf')" *ngIf="frameworkCtrl.isTestFormDirectConstruction()" class="button is-small is-success">
                  <i class="fa fa-plus" aria-hidden="true"></i>
                </button>
                <button [disabled]="isReadOnly()" (click)="itemBankCtrl.addQuestionToTestlet(question)" [title]="lang.tra('ie_add_item_to_tf')" *ngIf="itemBankCtrl.isCustomTestlet()" class="button is-small is-success">
                  <i class="fa fa-plus" aria-hidden="true"></i>
                </button>
                <button (click)="itemBankCtrl.selectQuestion(question, true)" [title]="lang.tra('auth_view_page')" class="button  is-small" [class.is-info]="question === itemBankCtrl.currentQuestion">
                  <i class="fa fa-eye" aria-hidden="true"></i>
                </button>
                <button [disabled]="isReadOnly() || saveLoadCtrl.isLoadingQuestion" (click)="itemBankCtrl.duplicateQuestion(question)" [title]="lang.tra('ie_duplicate_item')" *ngIf="!frameworkCtrl.isFrameworkView" class="button is-small">
                  <i class="fa fa-clone" aria-hidden="true"></i>
                </button>
              </ng-container>
              <ng-container *ngIf="question.type === ItemType.SEQUENCE">
                <button (click)="itemBankCtrl.selectQuestion(question, true); itemBankCtrl.switchToEditorView();" title="view sequence" class="button  is-small">
                  <i class="fa fa-folder-open" aria-hidden="true"></i>
                </button>
              </ng-container>
            </div>
          </div>
        </td>
      </tr>
    </table>
    <!--<div style="padding-left:25em; max-width:100%; overflow:auto">
      <table class="table item-table is-bordered is-condensed">
        <tr >
          <th  *ngIf="frameworkCtrl.asmtFmrk.isExposureComputed && frameworkCtrl.isTestformLOFT()" style="white-space: nowrap;">
            <tra slug="ie_no_testlets"></tra>
          </th>
          <th  *ngIf="frameworkCtrl.asmtFmrk.isExposureComputed && frameworkCtrl.isTestformLOFT()" style="white-space: nowrap;">
            <tra slug="ie_est_exp"></tra>
          </th>
          <th *ngFor="let param of frameworkCtrl.asmtFmrk.primaryDimensions" 
            [style.display]="param.isHidden || !isViewable(param) ? 'none' : ''" 
            [ngStyle]="{'background-color':frameworkCtrl.parseParamColor(param.color)}"
            title="{{param.code}} {{param.name}}"
          >
            {{param.code}}
            <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
              <a (click)="filterParams(param)" style="white-space: nowrap;">
                <i class="fa fa-search"></i>
                {{renderFilter(param)}}
              </a>
            </div>
          </th>
          <th *ngFor="let param of frameworkCtrl.asmtFmrk.secondaryDimensions" 
            [style.display]="param.isHidden || !isViewable(param) ? 'none' : ''" 
            [ngStyle]="{'background-color':frameworkCtrl.parseParamColor(param.color)}"
            title="{{param.code}} {{param.name}}"
          >
            {{param.code}}
            <div *ngIf="itemFilterCtrl.isQuestionFiltersActive.value">
              <a (click)="filterParams(param)" style="white-space: nowrap;">
                <i class="fa fa-search"></i>
                {{renderFilter(param)}}
              </a>
            </div>
          </th>
          <th *ngIf="!paramsOrDimensionsExist()">
            <div style="font-weight: 500; font-style: italic; white-space: nowrap;"><tra slug="audit_no_params"></tra></div>
            <div><a><i class="fa"></i></a></div>
          </th>
        </tr>
        <tr *ngFor="let question of itemBankCtrl.itemList; let questionIndex = index" [class.is-sequence-row]="question.type === ItemType.SEQUENCE">
          <td *ngIf="frameworkCtrl.asmtFmrk.isExposureComputed && frameworkCtrl.isTestformLOFT()">
            {{question.quadrantFreq}}
          </td>
          <td *ngIf="frameworkCtrl.asmtFmrk.isExposureComputed && frameworkCtrl.isTestformLOFT()">
            {{question.estimatedExposure}}
          </td>
          <td *ngFor="let param of frameworkCtrl.asmtFmrk.primaryDimensions"  
            style="white-space:nowrap;" 
            [style.display]="param.isHidden || !isViewable(param) ? 'none' : ''" 
            [ngStyle]="{'background-color':frameworkCtrl.parseParamColor(param.color)}"
            title="{{param.code}} {{param.name}}"
          >
            <framework-dimension-input 
              [frameworkCtrl]="frameworkCtrl" 
              [param]="param" 
              [question]="question" 
              [questionImpressions]="itemBankCtrl.questionImpressions" 
              [changeCounter]="question.__changeCounter" 
              [enabled]="itemEditCtrl.isCurrentEnabledQuestion(question) && !isReadOnly()">
            </framework-dimension-input>
          </td>
          <td *ngFor="let param of frameworkCtrl.asmtFmrk.secondaryDimensions" 
            style="white-space:nowrap;"
            [style.display]="param.isHidden || !isViewable(param) ? 'none' : ''"
            [ngStyle]="{'background-color':frameworkCtrl.parseParamColor(param.color)}"
            title="{{param.code}} {{param.name}}"
          >
            <framework-dimension-input 
              [frameworkCtrl]="frameworkCtrl" 
              [param]="param" 
              [question]="question" 
              [questionImpressions]="itemBankCtrl.questionImpressions" 
              [changeCounter]="question.__changeCounter" 
              [enabled]="itemEditCtrl.isCurrentEnabledQuestion(question) && !isReadOnly()">
            </framework-dimension-input>
          </td>
          <td *ngIf="!paramsOrDimensionsExist()" style="white-space:nowrap;"></td>
        </tr>
      </table>
    </div>-->
  </div>
  <div class="question-review" *ngIf="itemBankCtrl.currentQuestion">
    <h4 style="border-top: 1px solid #ccc; padding-top: 1em;">
      <tra slug="ie_item_preview"></tra>
      <button class="button is-small" (click)="itemBankCtrl.switchToEditorView()"><tra slug="ie_edit"></tra></button>
    </h4>
    <question-runner 
      [currentQuestion]="itemBankCtrl.getCurrentQuestionContent()" 
      [questionState]="itemBankCtrl.activeQuestionState"
      [isSubmitted]="itemEditCtrl.isLocked" 
      [isPrintMode]="printViewCtrl.isResultsPrint"
    ></question-runner>
  </div>
</div>

<div style="margin-top:1em;">
  <div class="pagination">
    <button (click)="itemFilterCtrl.prevItemListPage()" class="button is-small">Prev.</button>
    <button (click)="itemFilterCtrl.nextItemListPage()" class="button is-small">Next</button>
    <span><tra slug="ie_page"></tra></span>
    <span>{{itemBankCtrl.currentItemListPage}}</span>
    <span>of {{itemBankCtrl.totalItemListPages}}</span>
  </div>
</div>

<div class="custom-modal" *ngIf="cModal()">
    <div class="modal-contents">
        <workflow-group-assignment-modal
          [itemBankCtrl]="itemBankCtrl"
          [questionStages]="cmc().questionStages"
          [questionInfoByStageOrder]="cmc().questionInfoByStageOrder"
        ></workflow-group-assignment-modal>
        <modal-footer [confirmButton]="false" [pageModal]="pageModal" [closeMessage]="'btn_close'"></modal-footer>
    </div>
</div>