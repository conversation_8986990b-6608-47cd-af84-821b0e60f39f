import {Component, ElementRef, Input, On<PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';

// app services
import {EditingDisabledService} from '../editing-disabled.service';


import {ItemSetPreviewCtrl} from '../item-set-editor/controllers/preview';
import {ItemBankSaveLoadCtrl} from '../item-set-editor/controllers/save-load';
import {AssetLibraryCtrl} from '../item-set-editor/controllers/asset-library';
import {ItemSetFrameworkCtrl} from '../item-set-editor/controllers/framework';
import {ItemBankAuditor} from '../item-set-editor/controllers/audits';
import {ItemEditCtrl} from '../item-set-editor/controllers/item-edit';
import {ItemBankCtrl} from '../item-set-editor/controllers/item-bank';
import {MemberAssignmentCtrl} from '../item-set-editor/controllers/member-assignment';
import {ItemFilterCtrl} from '../item-set-editor/controllers/item-filter';
import {PanelCtrl} from '../item-set-editor/controllers/mscat';
import {ItemSetPrintViewCtrl} from '../item-set-editor/controllers/print-view';
import {ItemSetPublishingCtrl} from '../item-set-editor/controllers/publishing';
import {FrameworkQuadrantCtrl} from '../item-set-editor/controllers/quadrants';
import {TestFormGen} from '../item-set-editor/controllers/testform-gen';
import {TestletCtrl} from '../item-set-editor/controllers/testlets';
import {ItemBankUtilCtrl} from '../item-set-editor/controllers/util';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import {ItemType} from '../models';
import {
  DimensionType,
  IAssessmentFrameworkDimensionDetail,
  IAssessmentParameterViewFilter,
  testletFormConstructionMethod
} from "../item-set-editor/models/assessment-framework";
import { LangService } from '../../core/lang.service';
import { IItemTag } from '../item-tag/item-tag.component';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { AuthScopeSettingsService } from '../auth-scope-settings.service';
import { PARAM_SPECIAL_FLAGS, ItemParameterType } from '../framework-dimension-editor/model';
import { IHistoricalItemRegisterModelTw, IHistoricalTd } from 'src/app/ui-testrunner/models';
import { Subscription } from 'rxjs';import { LoginGuardService } from 'src/app/api/login-guard.service';
import { IWorkflowStage } from '../item-workflow-section/model';


@Component({
  selector: 'widget-item-listing',
  templateUrl: './widget-item-listing.component.html',
  styleUrls: ['./widget-item-listing.component.scss']
})
export class WidgetItemListingComponent implements OnInit, OnDestroy {

  
  @Input() assetLibraryCtrl:AssetLibraryCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl
  @Input() auditCtrl:ItemBankAuditor
  @Input() itemBankCtrl:ItemBankCtrl
  @Input() itemEditCtrl:ItemEditCtrl
  @Input() itemFilterCtrl:ItemFilterCtrl
  @Input() memberAssignmentCtrl:MemberAssignmentCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() previewCtrl:ItemSetPreviewCtrl
  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() publishingCtrl: ItemSetPublishingCtrl
  @Input() quadrantCtrl: FrameworkQuadrantCtrl
  @Input() saveLoadCtrl:ItemBankSaveLoadCtrl
  @Input() testFormGen: TestFormGen
  @Input() testletCtrl: TestletCtrl
  @ViewChild('tagInput') tagInput: ElementRef;

  @ViewChild('questionListing', { static: false }) questionListingEl: ElementRef;
  @ViewChild('parameterTable', { static: false }) parameterTableRef: ElementRef;
  
  public util = new ItemBankUtilCtrl();

  ItemType = ItemType
  showSavedFilters: boolean = false;
  excludeHidden: boolean = false;

  ItemParameterType = ItemParameterType;
  limitedParameterScopeInput:string = ''
  limitedParameterScope?:string[];
  activeParameterTypes:Map<ItemParameterType, boolean> = new Map();
  
  subscription: Subscription = new Subscription();

  parameterTypes = [
    {slug: ItemParameterType.STRUCT,    caption: 'Structural'},
    {slug: ItemParameterType.CONTENT,    caption: 'Content'},
    {slug: ItemParameterType.HISTORICAL, caption: 'Historical I.R.'},
    {slug: ItemParameterType.STATS,      caption: 'Psych. Stats'},
  ]

  pageModal: PageModalController;


  constructor(
    private editingDisabled: EditingDisabledService,
    public lang: LangService,
    private authScopeSettings: AuthScopeSettingsService,
    private pageModalService: PageModalService,
    private loginGuard: LoginGuardService
  ) { }

  ngOnInit(): void {
    this.activeParameterTypes.set(ItemParameterType.STRUCT, true);
    this.activeParameterTypes.set(ItemParameterType.CONTENT, true);
    this.itemBankCtrl.loadExpectedAnswers();
    this.subscription.add(this.lang.languageInitialized.subscribe(() => this.reloadExpectedAnswers()));
    if(testletFormConstructionMethod.includes(this.frameworkCtrl.asmtFmrk?.testFormType)){
      this.quadrantCtrl.refreshQuadrantItems();
    }
    this.pageModal = this.pageModalService.defineNewPageModal();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true);

  isParamTypeActive(paramTypeSlug:ItemParameterType){
    return !! this.activeParameterTypes.get(paramTypeSlug)
  }
  toggleParamTypeActive(paramTypeSlug:ItemParameterType){
    this.activeParameterTypes.set(paramTypeSlug, !this.isParamTypeActive(paramTypeSlug) )
  }

  getHistoricalItemProps(item_id:number, td_slug:string, propSlug:string) : number | string | null {
    const byTdSlug = this.itemBankCtrl.historicalItemRegister.get(item_id);
    if (byTdSlug){
      const itemParams = byTdSlug.get(td_slug);
      if (itemParams){
        return itemParams[propSlug]
      }
    }
    return;
  }

  getHistoricalTw(){
    return this.itemBankCtrl.itemRegisterSummary.test_windows;
  }

  getHistoricalTdSelected(){
    const tds:IHistoricalTd[] = [];
    for (let tw of this.itemBankCtrl.itemRegisterSummary.test_windows){
      for (let td of tw.test_designs){
        if (this.isHistoricalTdSelected(td.slug)){
          tds.push(td)
        }
      }
    }
    return tds;
  }

  isHistoricItemInTd(item_id:number, tdSlug:string){
    const item = this.itemBankCtrl.historicalItemRegister.get(item_id);
    if (item){
      const tdItemProps = item.get(tdSlug)
      if (tdItemProps){
        return true
      }
    }
    return false;
  }

  historicalPropVal(item_id:number, tdSlug:string, propSlug:string){
    const item = this.itemBankCtrl.historicalItemRegister.get(item_id);
    if (item){
      const tdItemProps = item.get(tdSlug)
      if (tdItemProps){
        return tdItemProps[propSlug]
      }
    }
  }

  isHistoricalTdSelected(td_slug:string){
    const selected_td_slugs = this.itemBankCtrl.itemRegisterSummary.selected_td_slugs;
    return !!selected_td_slugs.get(td_slug)
  }

  toggleHistoricalTdSelection(td_slug:string){
    const selected_td_slugs = this.itemBankCtrl.itemRegisterSummary.selected_td_slugs;
    selected_td_slugs.set(td_slug, !selected_td_slugs.get(td_slug));
  }

  toggleHistoricalTwSelection(tw:IHistoricalItemRegisterModelTw){
    tw.is_selected = !tw.is_selected;
    // then check to ensure that all remaining selected test designs are from available test windows
    const selected_td_slugs = this.itemBankCtrl.itemRegisterSummary.selected_td_slugs;
    for (let td of tw.test_designs){
      selected_td_slugs.set(td.slug, false);
    }
  }

  filterParams(param: IAssessmentFrameworkDimensionDetail) {
    const prefix = this.getParamPrefix(param);
    const isLangIndependent = param.config.special && param.config.special[PARAM_SPECIAL_FLAGS.LANG_INDEPENDENT];
    if (param.type === DimensionType.NUMERIC) {
      this.util.replaceRangeProp(this.itemFilterCtrl.filterSettings, prefix+param.code, true, isLangIndependent ? this.lang.c() : undefined);  
    } else {
      this.util.replaceProp(this.itemFilterCtrl.filterSettings, prefix+param.code, true, isLangIndependent ? this.lang.c() : undefined);
    }
    this.itemFilterCtrl.updateItemFilter();    
  }
  
  renderFilter(param: IAssessmentFrameworkDimensionDetail): string {
    const prefix = this.getParamPrefix(param);
    let filterValue = this.itemFilterCtrl.filterSettings[prefix+param.code]; 
    if(param.config.special && param.config.special[PARAM_SPECIAL_FLAGS.LANG_INDEPENDENT]) {
      filterValue = filterValue?.val;
    }
    if (param.type === DimensionType.NUMERIC) {
      if (typeof filterValue !== 'undefined') {
        const values = filterValue.split('->');
        if (values.length === 2) {
          if (values[0] !== '' && values[1] !== '') {
            filterValue = filterValue.replace('->','-');
          } else if (values[0] === '') {
            filterValue = filterValue.replace('->',' < ');
          } else {
            filterValue = filterValue.replace('->',' > ');
          }
        }
      }
    }
    return filterValue;
  }

  getParamPrefix(param: IAssessmentFrameworkDimensionDetail) {
    if(param.config.special && param.config.special[PARAM_SPECIAL_FLAGS.LANG_INDEPENDENT]) {
      if(this.lang.c() === 'fr') {
        return 'langLink.meta.'
      }
    }
    return 'meta.'
  }

  isEaLoaded = true;
  isEaLoading = false;
  async reloadExpectedAnswers(){
    this.isEaLoading = true;
    await this.itemBankCtrl.loadExpectedAnswers();
    this.isEaLoading = false;
    this.isEaLoaded = true;
  }

  saveFilter() {
    const filterName = prompt(`Name this filter`);
    if (filterName) {
      let savedFilters: IAssessmentParameterViewFilter[] = this.frameworkCtrl.asmtFmrk.filters;
      if (!savedFilters) {
        savedFilters = [];
        this.frameworkCtrl.asmtFmrk.filters = savedFilters;
      }
      const filter: IAssessmentParameterViewFilter = {
        name: filterName,
        filter: Object.assign({},this.itemFilterCtrl.filterSettings)
      }
      savedFilters.push(filter);
      this.showSavedFilters = true;
      this.saveLoadCtrl.saveChanges();
    }
  }

  removeSavedFilter(index: number) {
    const filters = this.frameworkCtrl.asmtFmrk.filters;
    if (filters && filters.length) {
      filters.splice(index, 1);
    }
    this.saveLoadCtrl.saveChanges();
  }
  
  applySavedFilter(savedFilter: IAssessmentParameterViewFilter) {
    this.itemFilterCtrl.filterSettings = Object.assign({},savedFilter.filter);
    this.itemFilterCtrl.updateItemFilter();    
  }
  
  viewSavedFilters() {
    this.showSavedFilters = !this.showSavedFilters; 
  }

  hasSavedFilters() {
    const filters = this.frameworkCtrl.asmtFmrk.filters;
    return filters && filters.length;
  }
  
  paramsOrDimensionsExist(): boolean {
    const exist = 
      !!(this.frameworkCtrl.asmtFmrk.primaryDimensions && this.frameworkCtrl.asmtFmrk.primaryDimensions.length) ||
      !!(this.frameworkCtrl.asmtFmrk.secondaryDimensions && this.frameworkCtrl.asmtFmrk.secondaryDimensions.length);
    return exist;
  }


  getParamDisplay(param) {
    return param.isHidden || !this.isViewable(param) ? 'none' : ''
  }

  addTag(tag: IItemTag) {
    this.itemFilterCtrl.addTag(tag);
    this.tagInput.nativeElement.blur();
  }

  isViewable(param) {
    return this.authScopeSettings.isItemParamViewable(param)
  }

  isDimensionEnabled(question, param){
    const isVisible = this.itemEditCtrl.isCurrentEnabledQuestion(question) && (!this.isReadOnly() || this.isViewable(param))
    return isVisible
  }

  isColEnabled(question){
    const isVisible = this.itemEditCtrl.isCurrentEnabledQuestion(question) && !this.isReadOnly()
    return isVisible
  }

  isNotSearchable(param){
    return this.isReadOnly() && !this.isViewable(param)
  }

  toggleExcludeBlankItemVersions(){
    this.itemFilterCtrl.isExcludeBlankItemVersions = !this.itemFilterCtrl.isExcludeBlankItemVersions
    this.itemFilterCtrl.updateItemFilter();
  }

    /** Prompt user for filter value for a given column, then apply it */
    addNewUserFilter(filterObj, filterProp:string){
      this.util.replaceProp(filterObj, filterProp, true);
      this.itemFilterCtrl.updateItemFilter();
    }

  /**
   * Since the list of questions stickied on the left is a separate table from the paramter table, find the height of the matching parameter table row to keep them aligned
   * @param qRowIndex Index of the question row (the same in the questions and parameter table)
   * @returns The pixel height of the row in the parameter table
   */
  getRowHeight(qRowIndex:number):number {
    if (!this.parameterTableRef) return;
    const parameterTable: HTMLTableElement = this.parameterTableRef.nativeElement;
    const rowElement: HTMLTableRowElement = parameterTable.rows[qRowIndex + 1]; // +1 to account for header
    return(rowElement.offsetHeight);
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }
  cmc() { return this.cModal().config; }

  /**
   * Prepare data for and start the question multi-assignment modal.
   * It takes the questions filtered for in the table, that are already in stages which the user is allowed to change assignments for.
   * If there are questions that can be assigned, open the modal.
   */
  multiAssignModalStart(){

    /** Helper function - return whether current user allowed to change assignments of passed stage */
    const canUserAssignToStage = (stage: IWorkflowStage):boolean => {
      const currActualRole = this.itemBankCtrl.getCurrActualRole();
      return stage.assignerRoles.includes(currActualRole)
    }

    const currFilterQuestions = this.itemFilterCtrl.filteredQuestions;
    const questionStages = [];
    const questionInfoByStageOrder = {}

    // Organize currently filtered for questions by stage
    currFilterQuestions.forEach(question => {

      const stageOrder = this.itemBankCtrl.getQuestionStage(question.id)?.order
      const stage = this.itemBankCtrl.getStageByOrder(stageOrder)
      
      // Skip question if it's either not in a stage, or user can't change assignments for its stage
      if (!stage || !canUserAssignToStage(stage)) return;

      if (!questionStages.includes(stage)) questionStages.push(stage)
      
      // Minimal info about the question, stored by stage order
      const questionInfo = {
        id: question.id,
        label: question.label,
        currAssignees: this.itemBankCtrl.renderQuestionStageAssignees(question.id),
        isSelected: true, // By default select all
      }
      if (!questionInfoByStageOrder[stageOrder]) questionInfoByStageOrder[stageOrder] = []
      questionInfoByStageOrder[stageOrder].push(questionInfo)

    })

    // Don't open the modal if nothing is valid to assign
    if (!questionStages.length) return this.loginGuard.quickPopup('No questions to be assigned.')

    // Sort the stages in ascending order
    questionStages.sort((a, b) => a.order - b.order);

    // Pass the question and stage info into the new modal
    const config = {
      questionStages,
      questionInfoByStageOrder,
    }
    this.pageModal.newModal({type: '', config, finish: () => {}});
  }
  
}
