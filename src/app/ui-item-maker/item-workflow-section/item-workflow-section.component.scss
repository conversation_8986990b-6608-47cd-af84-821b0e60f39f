.non-clickable-step {
  pointer-events: none;
  opacity: 0.5;
}

::ng-deep .mat-step {
  .mat-step-header-selected {
    background: none;
  }
  .mat-step-header {
    &:hover {
      background: none;
    }
    .mat-step-icon-state-edit {
      background-color: rgba(0, 0, 0, 0.54);
    }
  }
}

.has-text-orange {
  color: orange;
}

.stage-status-btn {
  white-space: normal;
  height: 100%;
}

.toggle-suggestion-compare{
  position: absolute; 
  right: 0; 
  top: 1.8em;
  margin-right: 2em; 
  height: 1.5em;
  width: 8em;
}