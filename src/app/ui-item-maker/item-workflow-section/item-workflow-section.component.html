<twiddle [state]="itemWorkflowTwiddle" [caption]="'Item Workflow'"></twiddle>
<div *ngIf="itemWorkflowTwiddle.value">
  <div *ngIf="activeStage">
  
    <mat-vertical-stepper [selectedIndex]="activeStage.order - 1" orientation="vertical" [disableRipple]="true" [linear]="true" #stepper (selectionChange)="onStepSelectionChange($event)">
      
      <mat-step 
        *ngFor="let workflowStage of workflowStages" 
        [completed]="isPrevStage(workflowStage.order)"
      >
        <!-- Stage title -->
        <ng-template matStepLabel>
          <span 
            [class.has-text-success] = "isPrevStage(workflowStage.order) && !isEditStageOrange(workflowStage)"
            [class.has-text-orange] = "isEditStageOrange(workflowStage)"
          >
            <tra slug={{workflowStage.slug}}></tra>
            <span *ngIf="workflowStage.isEditStage">
              ({{activeStage.isEditStage ? '#' : ''}}{{numEnterEdit || '0'}})
            </span>
          </span>
          <ng-container *ngIf="(workflowStage.isReviewStage && numEnterReview) || ( workflowStage.isEditStage && numEnterEdit)">
            <button 
            class="button is-small is-link toggle-suggestion-compare" 
            [class.is-loading]="getCompareButtonState(workflowStage.order, ButtonState.LOADING)"
            [class.is-success]="getCompareButtonState(workflowStage.order, ButtonState.LOADED)"
            [class.is-danger]="getCompareButtonState(workflowStage.order ,ButtonState.ERROR)"
            (click)="toggleIsComparingViewAvoidPropagation($event, workflowStage.order)"
            >
            <tra [slug]="getCompareButtonSlug(workflowStage.order)"></tra>
          </button>
          </ng-container>
        </ng-template>
  
          
        <!-- User assignment -->
        <div *ngIf="!workflowStage.isFinalStage && !isItemBankUserLoading" style="margin-bottom:1em">
          <chip-selector
            [potentialItems]="potentialAssignees" 
            [selectedItems]="selectedAssignees"
            [itemIdProp]="'uid'"
            [caption]="'auth_workflow_assign_users'"
            [placeholder]="'auth_workflow_new_users'"
            [isDisabled]="!canUserAssign(workflowStage.order)"
            [renderItemOption]="renderAssigneeOption"
            [renderItemChip]="renderAssigneeChip"
            [filterItems]="filterAssignees"
            (selectionChanged)="setAssignmentTouched()"
          >
          </chip-selector>
          <!-- Save user assignment -->
          <div class="columns">
            <div class="column is-half is-offset-half">
              <button 
              (click)="initSaveAssignment()"
              class="button is-small is-fullwidth is-link"
              [class.is-loading]="currAssignmentState==AssignmentState.SENDING"
              [class.is-success]="currAssignmentState==AssignmentState.SENT"
              [class.is-danger]="currAssignmentState==AssignmentState.ERROR"
              [disabled]="!canUserAssign(workflowStage.order) || !isAssignmentTouched"
              >
                <tra slug={{getSaveAssignmentSlug()}}></tra>
              </button>
            </div>
          </div>
        </div>
  
        <!--Most stages - sign off and move to the next one (unless last step) -->
        <div *ngIf="!workflowStage.isReviewStage && !workflowStage.isEditStage">
          <button 
            class="button is-small is-fullwidth is-primary stage-status-btn"
            [class.is-outlined]="isSignedOff"
            [disabled]="!canUserChange(workflowStage.order)"
            (click)="toggleSignedOff()"
          ><tra slug={{getSignOffSlug()}}></tra></button>
  
          <button 
            *ngIf="isSignedOff && !workflowStage.isFinalStage" 
            class="button is-small is-fullwidth is-danger is-inverted"
            style="margin-top: 1em"
            (click)="initMoveToStage(workflowStage.order + 1)"
            [disabled]="!canUserChange(workflowStage.order)">
            <tra slug="auth_workflow_proceed_to"></tra>&nbsp;
            <tra slug={{getStageByOrder(workflowStage.order+1)?.slug}}></tra>
            </button>
        </div>

        <!--Review stage - two move options depending on choices -->
        <div *ngIf="workflowStage.isReviewStage">

          <div class="columns">
            <div class="column">
              <button 
              class="button is-small is-fullwidth is-primary stage-status-btn"
              [class.is-outlined]="isSignedOff"
              [disabled]="!canUserChange(workflowStage.order)"
              (click)="toggleSignedOff()"
            ><tra slug={{getSignOffSlug()}}></tra></button>
            </div>
            <div class="column">
              <button 
              class="button is-small is-fullwidth is-warning stage-status-btn"
              [class.is-outlined]="isEditReview"
              [disabled]="!canUserChange(workflowStage.order)"
              (click)="toggleEditReview()"
            ><tra slug={{getEditReviewSlug()}}></tra></button>
            </div>
          </div>
  
          <button 
          *ngIf="isSignedOff && isEditReview"
          class="button is-small is-fullwidth is-danger is-inverted"
          style="margin-top: 1em"
          (click)="initMoveToStage(workflowStage.order-1)"
          [disabled]="!canUserChange(workflowStage.order)">
            <tra slug="auth_workflow_proceed_back_to"></tra>&nbsp;
            <tra slug={{getStageByOrder(workflowStage.order-1)?.slug}}></tra>
          </button>
  
        </div>
  
        <!--Edit stage - two move options depending on choices -->
        <div *ngIf="workflowStage.isEditStage">

          <div class="columns">
            <div class="column">
              <button 
              class="button is-small is-fullwidth is-primary stage-status-btn"
              [class.is-outlined]="isDoneEditing"
              [disabled]="!canUserChange(workflowStage.order) || isChangesRequired"
              (click)="toggleDoneEditing()"
            ><tra slug={{getDoneEditingSlug()}}></tra></button>
            </div>
            <div class="column">
              <button 
              class="button is-small is-fullwidth is-warning stage-status-btn"
              [class.is-outlined]="isChangesRequired"
              [disabled]="!canUserChange(workflowStage.order) || isDoneEditing"
              (click)="toggleChangesRequired()"
            ><tra slug={{getChangesRequiredSlug()}}></tra></button>
            </div>
          </div>
  
          <button 
            *ngIf="isChangesRequired"
            class="button is-small is-fullwidth is-danger is-inverted"
            style="margin-top: 1em"
            (click)="initMoveToStage(workflowStage.order +1)"
            [disabled]="!canUserChange(workflowStage.order)">
              <tra slug="auth_workflow_proceed_to"></tra>&nbsp;
              <tra slug={{getStageByOrder(workflowStage.order+1)?.slug}}></tra>
          </button>
  
          <button 
          *ngIf="isDoneEditing"
          class="button is-small is-fullwidth is-danger is-inverted"
          style="margin-top: 1em"
          (click)="initMoveToStage(workflowStage.order+2)"
          [disabled]="!canUserChange(workflowStage.order)">
            <tra slug="auth_workflow_proceed_to"></tra>&nbsp;
            <tra slug={{getStageByOrder(workflowStage.order+2)?.slug}}></tra>
          </button>
  
        </div>
  
      </mat-step>
      
      <!-- Override defaults - already cpmpleted steps to show number, not icon -->
      <ng-template let-i="index" matStepperIcon="edit">
        {{ i + 1 }}
      </ng-template>
  
    </mat-vertical-stepper>
  
  </div>

  <!-- If question has no stage yet - special menu to put it into any stage -->
  <div *ngIf="!activeStage && !isStageStatusLoading" style="display: flex; flex-direction: column; align-items: center;">
    <p style="margin-bottom: 1em">
      This item has not been assigned a stage yet.
    </p>

    <div class="columns">
      <div class="column">
        <div class="select is-primary">
          <select [(ngModel)]="dropdownStageOrder" [disabled]="!canUserInitStageOldItem()">
            <option disabled [ngValue]="0">Select a stage...</option>
            <option
              *ngFor="let workflowStage of workflowStages"
              [ngValue]="workflowStage.order"
            >{{workflowStage.order}}. <tra slug={{workflowStage.slug}}></tra></option>
          </select>
        </div>
      </div>
  
      <div class="column">
        <button [disabled]="!dropdownStageOrder || !canUserInitStageOldItem()" class="button is-primary" (click)="initMoveToStage(dropdownStageOrder)">Assign Stage</button>
      </div>
    </div>

  </div>
</div>